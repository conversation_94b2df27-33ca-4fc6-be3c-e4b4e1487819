interface IBookLevel {

  //   getBookLevels(): Promise<any[]>
  //   createBookLevel(data: any): Promise<any>
  //   updateBookLevel(id: number, data: any): Promise<any>
  //   deleteBookLevel(id: number): Promise<any>

    findLevels(ids: number[]): Promise<any[]>

    getByIds(id: number[])
}

export abstract class IBookLevelService implements IBookLevel {
    abstract findLevels(ids: number[]): Promise<any[]>
    abstract getByIds(id: number[]): Promise<any[]>
}