import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import Decimal from 'decimal.js'
import moment from 'moment-timezone'
import { Repository } from 'typeorm'
import { ELocaleType, ExcelService } from '@/common'
import { ReferenceBook } from '@/entities'
import { QueryReadingTimeDto } from '@/modules/books/dto'
import { IBookRepo } from '@/modules/shared/interfaces'
import { getISBN, getName } from '@/utils/book.utitl'

@Injectable()
export class ReferenceService {
  constructor(
    @InjectRepository(ReferenceBook)
    private readonly referenceModel: Repository<ReferenceBook>,
    private readonly bookRepositories: IBookRepo,
    private readonly excelService: ExcelService,
  ) {}

  async exportForPublishers(
    query: QueryReadingTimeDto,
    locale: ELocaleType,
    publisherId?: number,
  ) {
    const reference = await this.referenceModel.query(`
      select
        contract_histories.book_id as bookId,
        region,
        sum(contract_histories.copies_count) as copiesCount
      from
        contract_histories
        inner join schools on schools.id = contract_histories.school_id
        ${publisherId ? 'inner join books on books.id = contract_histories.book_id' : ''}
        ${
          publisherId ? 'inner join publishers on publishers.id = books.publisher_id' : ''
        }
      where
        unix_timestamp(contract_histories.created_at) > ${
          query.startTime
        }  and  unix_timestamp(contract_histories.created_at) < ${query.endTime} ${
      publisherId ? `and publishers.id = ${publisherId}` : ''
    } 
      group by
        contract_histories.book_id,
        schools.region
    `)

    const currents = await this.referenceModel.query(`
        select
          schools.region,
          contract_histories.book_id as bookId,
          sum(contract_histories.copies_count) as copiesCount
        from
          contract_histories
          inner join schools on schools.id = contract_histories.school_id
          inner join books on books.id = contract_histories.book_id
          ${
            publisherId
              ? 'inner join publishers on publishers.id = books.publisher_id'
              : ''
          }
      where
         unix_timestamp(contract_histories.created_at) < ${query.endTime} 
         ${publisherId ? `and publishers.id = ${publisherId}` : ''}
      group by
        schools.region,
        contract_histories.book_id
    `)
    const books = currents.length
      ? await this.bookRepositories.listBooks(
          {
            ids: [
              ...new Set(
                currents
                  .map((item) => item.bookId)
                  .concat(reference.map((item) => item.bookId)),
              ),
            ] as number[],
          },
          {
            relations: ['publisher', 'authors'],
            fields: ['id', 'name', 'isbn', 'publishedAt', 'price', 'deletedAt'],
            withDeleted: true,
          },
        )
      : []

    return this.excelService.buildRegionExcel({
      specification: publisherId
        ? []
        : [{ keyName: 'isDeleted', displayName: '是否刪除' }],
      data: books.map((book) => {
        const item = reference.filter((item) => item.bookId === book.id) || []
        const current = currents.filter((current) => current.bookId === book.id)
        return {
          startTime: moment
            .tz(query.startTime * 1000, 'Asia/Hong_Kong')
            .format('YYYY-MM-DD'),
          endTime: moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
          publisherName: getName(book.publisher?.name, locale),
          publisherGroup: getName(book.publisher?.publisherGroupName, locale),
          isbn: getISBN(book.isbn),
          name: getName(book.name, locale),
          author: book.authors?.map((author) => getName(author.name, locale)).join(','),
          publishedAt: book.publishedAt
            ? moment.tz(book.publishedAt, 'Asia/Hong_Kong').format('YYYY')
            : '',
          ...item.reduce((acc, cur) => {
            acc[`${cur.region}CopiesCount`] =
              acc[`${cur.region}CopiesCount`] || 0 + Number(cur.copiesCount)
            return acc
          }, {}),
          ...current.reduce((acc, cur) => {
            acc[`all${cur.region}CopiesCount`] =
              acc[`all${cur.region}CopiesCount`] || 0 + Number(cur.copiesCount)
            return acc
          }, {}),
          copiesCount: item.reduce((acc, cur) => acc + Number(cur.copiesCount), 0),
          allCopiesCount:
            current.reduce((acc, cur) => acc + Number(cur.copiesCount), 0) || 0,
          price: book.price ? new Decimal(book.price).div(100).toNumber() : '-',
          isDeleted: book.deletedAt ? '是' : '否',
          isAdded: item.length ? 'Yes' : 'No',
        }
      }),
      name: `platformPublisherReference.${locale}`,
    })
  }

  async exportForSchool(
    publisherId: number,
    query: QueryReadingTimeDto,
    locale: ELocaleType,
  ) {
    const references = await this.referenceModel.query(`
      select
        schools.id as schoolId,
        sum(contract_histories.copies_count) as copiesCount,
        sum(contract_histories.book_count) as bookCount
      from
        contract_histories
        inner join schools on schools.id = contract_histories.school_id
        inner join books on books.id = contract_histories.book_id
        inner join publishers on publishers.id = books.publisher_id
      where
        unix_timestamp(contract_histories.created_at) > ${query.startTime}  and  unix_timestamp(contract_histories.created_at) < ${query.endTime} and publishers.id = ${publisherId}
      group by
        schools.id
    `)

    const currents = await this.referenceModel.query(`
      select
        schools.id as schoolId,
        sum(contract_histories.copies_count) as copiesCount,
        count(distinct(contract_histories.book_id)) as bookCount
      from
        contract_histories
        inner join schools on schools.id = contract_histories.school_id
        inner join books on books.id = contract_histories.book_id
        inner join publishers on publishers.id = books.publisher_id
      where
        publishers.id = ${publisherId}
        and unix_timestamp(contract_histories.created_at) < ${query.endTime} 
      group by
        schools.id
    `)

    const schools = currents.length
      ? await this.referenceModel.query(
          `select id, name, region from schools where id in (${currents
            .map((item) => item.schoolId)
            .join(',')})`,
        )
      : []

    return this.excelService.buildExcel({
      name: `platformPublisherSchoolReference.${locale}`,
      data: currents.map((item) => {
        const school = schools.find((school) => school.id === item.schoolId)
        const referenceSchool = references.find(
          (reference) => reference.schoolId === item.schoolId,
        )
        return {
          startTime: moment
            .tz(query.startTime * 1000, 'Asia/Hong_Kong')
            .format('YYYY-MM-DD'),
          endTime: moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
          school: getName(school.name, locale),
          region: getName(school.region, locale),
          allCopiesCount: Number(item.copiesCount),
          [`all${school.region}CopiesCount`]: Number(item.copiesCount),
          copiesCount: Number(referenceSchool?.copiesCount || 0),
          [`${school.region}CopiesCount`]: Number(referenceSchool?.copiesCount || 0),
          [`${school.region}BookCount`]: Number(referenceSchool?.bookCount || 0),
          bookCount: Number(referenceSchool?.bookCount || 0),
          [`all${school.region}BookCount`]: Number(item?.bookCount || 0),
          allBookCount: Number(item?.bookCount || 0),
          isAdded: referenceSchool ? 'Yes' : 'No',
        }
      }),
    })
  }
}
