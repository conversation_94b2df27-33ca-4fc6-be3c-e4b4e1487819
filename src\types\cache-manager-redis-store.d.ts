declare module 'cache-manager-redis-store' {
  interface RedisStoreOptions {
    [key: string]: any
    host?: string
    port?: number
    password?: string
    db?: number
    ttl?: number
    max?: number
    auth_pass?: string
    family?: number
    keepAlive?: boolean
    prefix?: string
  }
  
  interface RedisStore {
    get<T>(key: string): Promise<T | undefined>
    set<T>(key: string, value: T, ttl?: number): Promise<void>
    del(key: string): Promise<void>
    reset(): Promise<void>
    keys(pattern?: string): Promise<string[]>
    ttl(key: string): Promise<number>
    wrap<T>(key: string, fn: () => Promise<T>, ttl?: number): Promise<T>
    getClient(): any
  }
  
  function redisStore(options?: RedisStoreOptions): RedisStore
  export = redisStore
}
