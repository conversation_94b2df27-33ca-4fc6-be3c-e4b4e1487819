import { CACHE_MANAGER } from '@nestjs/cache-manager'
import {
  Controller,
  Get,
  Inject,
  Param,
  ParseIntPipe,
  Query,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Cache } from 'cache-manager'
import {
  ApiBaseResult,
  ApiListResult,
  ApiPageResult,
  ClientAuth,
  CurrentUser,
  PageRequest,
} from '@/common'
import { EBookVersion, EUserType, OnlineOfflineStatus } from '@/enums'
import { IUserRepo } from '@/modules/shared/interfaces'
import { getBookIsHidden } from '@/modules/books/utils/bookhidden.util'
import {
  getReferenceRecommendKey,
  getSubscriptionHomepageBookListKey,
  getSubscriptionHomepageKey,
  getSubscriptionNewestKey,
  getSubscriptionRecommendKey,
  PAGE_SIZE,
} from '@/modules/constants'
import { prefetchFromCache } from '@/utils/cache.util'
import {
  getHomepageDto,
  HomepageDto,
  QueryFixedHomepageDto,
  ReferenceHotBookDto,
} from '../../books/dto'
import { IAuthorService, IBookshelfService, IBookService } from '@/modules/shared/interfaces'
import { BookListService, SchoolHomepageService, SchoolService, HomepageService } from '../services'

@ApiTags('Homepage')
@ApiExtraModels(HomepageDto)
@Controller('v1/client/homepage')
export class HomepageClientController {
  constructor(
    private readonly homepageService: HomepageService,
    private readonly bookService: IBookService,
    private readonly bookshelfService: IBookshelfService,
    private readonly schoolService: SchoolService,
    private readonly schoolHomepageService: SchoolHomepageService,
    private readonly userRepository: IUserRepo,
    private readonly authorService: IAuthorService,
    private readonly bookListService: BookListService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  @ClientAuth()
  @ApiOperation({ summary: 'get newest homepage' })
  @ApiPageResult(HomepageDto, 200)
  @Get('newest')
  async getNewestHomepage(
    @Query() data: QueryFixedHomepageDto,
    @CurrentUser() user: any
  ): Promise<any> {
    const pageIndex = data.pageIndex ?? 1
    const pageSize = data.limit ?? data.pageSize ?? 10
    const newestKey = getSubscriptionNewestKey(
      user.schoolId,
      user.isTeacher ? EUserType.TEACHER : EUserType.STUDENT
    )

    const { total, homepage, books, school } =
      pageIndex === 1 && pageSize === 9
        ? await prefetchFromCache(this.cacheManager, newestKey, async () =>
          this.getNewestPage(user, pageIndex, pageSize)
        )
        : await this.getNewestPage(user, pageIndex, pageSize)

    const shelf = await this.bookshelfService.findBookShelf(
      books.map((item) => item.id),
      user.userId,
      EBookVersion.SUBSCRIPTION
    )

    return {
      total,
      pageIndex,
      pageSize: pageSize,
      items: getHomepageDto({
        ...homepage,
        books: books
          .map((item) => ({
            ...item,
            isOnShelf: shelf.includes(item.id),
            isHidden: getBookIsHidden(item, school, user.isTeacher),
          }))
          .slice(0, pageSize),
      }),
    }
  }

  @ClientAuth()
  @ApiOperation({ summary: 'get hot homepage' })
  @ApiBaseResult(HomepageDto, 200)
  @Get('hot')
  async getHotHomepage(
    @Query() query: QueryFixedHomepageDto,
    @CurrentUser() user: any
  ): Promise<any> {
    const pageIndex = query.pageIndex ?? 1
    const pageSize = query.pageSize ?? PAGE_SIZE
    const skip = (pageIndex - 1) * pageSize
    const school = await this.schoolService.findOne({ where: { id: user.schoolId  } })
    const homepage = await this.homepageService.getHomepage(2)
    const level = this.bookService.getLevels(undefined, user)
    const data = await this.homepageService.getHotBooks(
      user.schoolId,
      pageSize,
      skip,
      user.isTeacher,
      {},
      level
    )
    const shelf = await this.bookshelfService.findBookShelf(
      data.items.map((item) => item.id),
      user.userId,
      EBookVersion.SUBSCRIPTION
    )

    return {
      ...getHomepageDto({
        ...homepage,
        books: data.items.map((item) => ({
          ...item,
          isOnShelf: shelf.includes(item.id),
          isHidden: getBookIsHidden(item, school, user.isTeacher),
        })),
      }),
      pageIndex,
      pageSize,
      total: data.total,
    }
  }

  @ClientAuth()
  @ApiOperation({ summary: 'get recommend homepage' })
  @ApiBaseResult(HomepageDto, 200)
  @Get('recommend/reference')
  async getReferenceRecommendHomepage(@CurrentUser() user: any): Promise<HomepageDto> {
    const recommendKey = getReferenceRecommendKey(
      user.schoolId,
      user.isTeacher ? EUserType.TEACHER : EUserType.STUDENT,
      user.gradeId,
      user.classId
    )

    const homepage = await prefetchFromCache(this.cacheManager, recommendKey, () =>
      this.schoolHomepageService.getReferenceRecommend(user.schoolId, user)
    )

    const shelf = await this.bookshelfService.findBookShelf(
      homepage.books.map((item) => item.id),
      user.userId,
      EBookVersion.REFERENCE
    )

    return getHomepageDto({
      ...homepage,
      books: homepage.books.map((book) => ({
        ...book,
        isOnShelf: shelf.includes(book.id),
      })),
    })
  }

  @ApiOperation({ summary: 'reference hot books' })
  @ApiPageResult(ReferenceHotBookDto, 200)
  @ClientAuth()
  @Get('hot/reference')
  async listReferenceHot(@CurrentUser() user: any, @Query() query: PageRequest) {
    const data = await this.homepageService.getReferenceHot(user.schoolId, query, {
      fields: ['id', 'coverUrl', 'name', 'isbn', 'description', 'url'],
      relations: ['authors', 'categories', 'labels'],
    })

    const shelf = await this.bookshelfService.findBookShelf(
      data.items.map((item) => item.id),
      user.userId,
      EBookVersion.REFERENCE
    )

    return {
      ...data,
      items: data.items.map((item) => ({ ...item, isOnShelf: shelf.includes(item.id) })),
    }
  }

  @ClientAuth()
  @ApiOperation({ summary: 'get recommend homepage' })
  @ApiBaseResult(HomepageDto, 200)
  @Get('recommend')
  async getRecommendHomepage(
    @Query() data: QueryFixedHomepageDto,
    @CurrentUser() user: any
  ): Promise<HomepageDto> {
    const recommendKey = getSubscriptionRecommendKey(
      user.schoolId,
      user.isTeacher ? EUserType.TEACHER : EUserType.STUDENT,
      user.gradeId,
      user.classId
    )

    console.log({ recommendKey })

    const { school, homepage } = await prefetchFromCache(
      this.cacheManager,
      recommendKey,
      async () => {
        let homepage: any
        const school = await this.schoolService.findOne({ where: { id: user.schoolId  } })

        let isFormSchoolHomepage = true

        if (user.isTeacher) {
          homepage = await this.schoolHomepageService.getTeacherRecommend(
            user.schoolId,
            school.isAllLevelForStaff ? undefined : school.staffLevelIds
          )
        } else {
          const student = await this.userRepository.findOne({
            where: { id: user.userId },
            relations: ['userClass'],
          })
          homepage = await this.schoolHomepageService.getStudentRecommend(
            user.schoolId,
            student.userClass.id,
            student.userClass.gradeId,
            school.isAllLevelForStudent ? undefined : school.studentLevelIds
          )
        }

        if (!homepage || homepage.books?.length === 0) {
          homepage = await this.homepageService.getTeacherRecommend(
            school.type,
            user.isTeacher
              ? school.isAllLevelForStaff
                ? undefined
                : school.staffLevelIds
              : school.isAllLevelForStudent
                ? undefined
                : school.studentLevelIds,
            user.isTeacher ? undefined : user.schoolId
          )
          isFormSchoolHomepage = false
        }

        const _books = await this.authorService.findAuthorNames(
          []
            .concat(homepage?.books?.map((x) => x.id))
            .concat(homepage?.booklist?.books?.map((x) => x.id))
        )

        if (homepage?.books) {
          homepage = {
            ...homepage,
            books: homepage.books.map((x) => {
              const appendAuthorInfo = _books.find((y) => y.id === x.id)
              return {
                ...x,
                ...appendAuthorInfo,
              }
            }),
          }
          if (isFormSchoolHomepage)
          {homepage = await this.schoolHomepageService.sortSchoolHomepageBooks(homepage)}
          else {homepage = await this.homepageService.sortHomepageBooks(homepage)}
        }

        if (homepage?.booklist?.books) {
          homepage = {
            ...homepage,
            booklist: await this.bookListService.sortBooklistBooks({
              ...homepage.booklist,
              books: homepage.booklist.books.map((x) => {
                const appendAuthorInfo = _books.find((y) => y.id === x.id)
                return {
                  ...x,
                  ...appendAuthorInfo,
                }
              }),
            }),
          }
        }
        return { school, homepage }
      }
    )

    const bookIds = homepage?.books?.length
      ? homepage?.books?.map((item) => item.id)
      : homepage?.booklist?.books?.map((item) => item.id)

    const shelf = await this.bookshelfService.findBookShelf(
      bookIds,
      user.userId,
      EBookVersion.SUBSCRIPTION
    )

    if (homepage?.books?.length) {
      const books = homepage.books.filter(
        (item) => item.status === OnlineOfflineStatus.ONLINE
      )
      homepage.books = books.map((item) => ({
        ...item,
        isOnShelf: shelf.includes(item.id),
        isHidden: getBookIsHidden(item, school, user.isTeacher),
      }))
    }
    if (homepage?.booklist?.books?.length) {
      const books = homepage.booklist.books.filter(
        (item) => item.status === OnlineOfflineStatus.ONLINE
      )

      homepage.booklist.books = books.map((item) => ({
        ...item,
        isOnShelf: shelf.includes(item.id),
        isHidden: getBookIsHidden(item, school, user.isTeacher),
      }))
    }

    return homepage ? getHomepageDto(homepage) : undefined
  }

  @ClientAuth()
  @ApiOperation({ summary: 'list homepage' })
  @ApiListResult(HomepageDto, 200)
  @Get()
  async getHomepage(@CurrentUser() user: any): Promise<HomepageDto[]> {
    const homepageKey = getSubscriptionHomepageKey(
      user.schoolId,
      user.isTeacher ? EUserType.TEACHER : EUserType.STUDENT
    )
    const { school, homepages } = await prefetchFromCache(
      this.cacheManager,
      homepageKey,
      async () => {
        const levels = user.isTeacher
          ? user.isAllLevelForStaff
            ? undefined
            : user.staffLevelIds
          : user.isAllLevelForStudent
            ? undefined
            : user.levels
        const school = await this.schoolService.findOne({ where: { id: user.schoolId  } })
        const homepages = await this.homepageService.listClientHomepage(levels)
        return { homepages, school }
      }
    )

    return Promise.all(
      homepages.map(async (homepage) => {
        const bookIds = homepage.books?.length
          ? homepage?.books?.map((item) => item.id)
          : homepage?.booklist?.books?.map((item) => item.id)
        const shelf = await this.bookshelfService.findBookShelf(
          bookIds,
          user.userId,
          EBookVersion.SUBSCRIPTION
        )
        if (homepage?.books?.length) {
          let books = homepage.books.filter(
            (item) => item.status === OnlineOfflineStatus.ONLINE
          )
          if (!user.isTeacher)
          {books = books.filter((item) => !item.hiddeSchoolIds.includes(user.schoolId))}
          homepage.books = books.map((item) => ({
            ...item,
            isOnShelf: shelf.includes(item.id),
            isHidden: getBookIsHidden(item, school, user.isTeacher),
          }))
        }
        if (homepage?.booklist?.books?.length) {
          let books = homepage.booklist.books.filter(
            (item) => item.status === OnlineOfflineStatus.ONLINE
          )
          if (!user.isTeacher)
          {books = books.filter((item) => !item.hiddeSchoolIds.includes(user.schoolId))}

          homepage.booklist.books = books.map((item) => {
            return {
              ...item,
              isOnShelf: shelf.includes(item.id),
              isHidden: getBookIsHidden(item, school, user.isTeacher),
            }
          })
        }
        return getHomepageDto(homepage)
      })
    )
  }

  @ClientAuth()
  @ApiOperation({ summary: 'get client homepage' })
  @ApiBaseResult(HomepageDto, 200)
  @Get(':id')
  async getHomepageBookList(
    @CurrentUser() user: any,
    @Param('id', ParseIntPipe) id: number,
    @Query() query: PageRequest
  ): Promise<any> {
    const { pageIndex = 1, pageSize = 10 } = query
    const subscriptionHomepageBookListKey = getSubscriptionHomepageBookListKey(
      user.schoolId,
      user.isTeacher ? EUserType.TEACHER : EUserType.STUDENT,
      id
    )
    const { school, homepage } = await prefetchFromCache(
      this.cacheManager,
      subscriptionHomepageBookListKey,
      async () => {
        const levels = user.isTeacher
          ? user.isAllLevelForStaff
            ? undefined
            : user.staffLevelIds
          : user.isAllLevelForStudent
            ? undefined
            : user.levels
        const school = await this.schoolService.findOne({ where: { id: user.schoolId  } })
        const homepage = await this.homepageService.listClientHomepageById(id, levels)
        return { homepage, school }
      }
    )
    let homepageClient
    if (homepage?.books) {
      homepageClient = await this.homepageService.sortHomepageBooks(homepage)
      const shelf = await this.bookshelfService.findBookShelf(
        homepage.books.map((item) => item.id),
        user.userId,
        EBookVersion.SUBSCRIPTION
      )
      homepage.books = homepage.books.map((item) => ({
        ...item,
        isOnShelf: shelf.includes(item.id),
        isHidden: getBookIsHidden(item, school, user.isTeacher),
      }))
      const paginatedBooks = this.paginateBooks(homepage.books, pageIndex, pageSize)
      homepageClient = {
        ...homepage,
        books: paginatedBooks,
        total: homepage.books.length,
        pageIndex,
        pageSize,
      }
    }
    return {
      ...getHomepageDto(homepageClient),
    }
  }

  private paginateBooks(books, pageNumber, pageSize) {
    const startIndex = (pageNumber - 1) * pageSize
    const endIndex = pageNumber * pageSize
    return books.slice(startIndex, endIndex)
  }

  private async getNewestPage(user: any, pageIndex: number, pageSize: number) {
    const school = await this.schoolService.findOne({ where: { id: user.schoolId  } })
    const homepage = await this.homepageService.getHomepage(3)

    const levels = user.isTeacher
      ? user.isAllLevelForStaff
        ? undefined
        : user.staffLevelIds
      : user.isAllLevelForStudent
        ? undefined
        : user.levels

    const { items: books, total } = await this.bookService.getNewestBooks(
      user.isTeacher ? undefined : user.schoolId,
      {
        pageIndex,
        pageSize,
      },
      levels
    )

    return { total, homepage, books, school }
  }
}
