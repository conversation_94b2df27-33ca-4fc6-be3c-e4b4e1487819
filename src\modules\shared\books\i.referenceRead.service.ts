import {QueryReadingTimeDto} from '@/modules/books/dto'
import {QuerySchoolReferenceBookStatisticDto} from '@/modules/books/dto/referenceBook.dto'

interface IReferenceRead {

}
export abstract class IReferenceReadService implements IReferenceRead{
    abstract getTotalBooks(userId: number): Promise<number>
    abstract getTopBooks(schoolId: number, limit: number): Promise<any>
    abstract getReadingUserIdsBySchool(schoolId: number, query: any): Promise<any>
    abstract listReadBooks(userId: number, schoolId: number, query: any): Promise<any>

    abstract groupByClass(schoolId: number, query: QueryReadingTimeDto)

    abstract statistic(schoolId: number, query: QuerySchoolReferenceBookStatisticDto)

    abstract startRead(bookId: number, userId: number, schoolId: number)

    abstract reading(bookId: number, userId: number, schoolId: number)

    abstract endRead(bookId: number, userId: number, schoolId: number)

    abstract exportUsers(schoolId: number, query: QuerySchoolReferenceBookStatisticDto)
}