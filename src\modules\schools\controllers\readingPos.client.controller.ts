import { Body, Controller, Post } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { ApiBaseR<PERSON>ult, ClientAuth, CurrentPlatform, CurrentUser } from '@/common'
import { IReadingPosService } from '@/modules/shared/interfaces'
import { CreateReadingPosDto, ReadingDto } from '../dto'

@ApiTags('Reading')
@ApiExtraModels(ReadingDto)
@Controller('v1/client/reading-pos')
export class ReadingPosClientController {
  constructor(private readonly readingPosService: IReadingPosService) {}

  @ApiOperation({ summary: 'report reading pos' })
  @ApiBaseResult(ReadingDto, 200)
  @Post()
  @ClientAuth()
  async reportReadingPos(
    @CurrentUser('userId') userId: number,
    @CurrentPlatform() platform: string,
    @Body() data: CreateReadingPosDto,
  ) {
    return this.readingPosService.create({
      userId,
      pos: data.pos,
      bookId: data.bookId,
      platform,
    })
  }
}
