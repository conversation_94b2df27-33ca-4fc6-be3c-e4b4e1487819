import {EBookVersion} from '@/enums'

interface IBookshelf {
    findOne(options: any): Promise<any>
    find(options?: any): Promise<any[]>
    create(data: any): Promise<any>
    update(id: number, data: any): Promise<any>
    delete(id: number): Promise<any>
}

export abstract class IBookshelfService implements IBookshelf {
    abstract findOne(options: any): Promise<any>
    abstract find(options?: any): Promise<any[]>
    abstract create(data: any): Promise<any>
    abstract update(id: number, data: any): Promise<any>
    abstract delete(id: number): Promise<any>

    abstract findBookShelf(bookIds: number[], userId: number, version: EBookVersion)
}