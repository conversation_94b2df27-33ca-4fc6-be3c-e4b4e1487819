import { CACHE_MANAGER } from '@nestjs/cache-manager'
import {
  Controller,
  Get,
  Inject,
  Param,
  ParseIntPipe,
  Query,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Cache } from 'cache-manager'
import R from 'ramda'
import { ApiBaseResult, ClientAuth, CurrentUser } from '@/common'
import { Book } from '@/entities'
import { EBookVersion, OnlineOfflineStatus } from '@/enums'
import { IBookshelfService } from '@/modules/shared/interfaces'
import { getBookIsHidden } from '@/modules/books/utils/bookhidden.util'
import { prefetchFromCache } from '@/utils/cache.util'
import { BookListDto, ClientQueryBookListDto, getBookListDto } from '../../books/dto'
import { BookListService, SchoolService } from '../services'

@ApiTags('BookLists')
@ApiExtraModels(BookListDto)
@Controller('v1/client/book-lists')
export class BookListClientController {
  constructor(
    private readonly bookshelfService: IBookshelfService,
    private readonly bookListService: BookListService,
    private readonly schoolService: SchoolService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  @ApiOperation({ summary: 'find a book list' })
  @ClientAuth()
  @ApiBaseResult(BookListDto, 200)
  @Get(':id')
  async getBookList(@Param('id', ParseIntPipe) id: number, @CurrentUser() user: any) {
    const school = await prefetchFromCache(
      this.cacheManager,
      `cacheKey.findOneSchool.${user.schoolId}`,
      () => this.schoolService.findOne({ where: { id: user.schoolId  } })
    )
    // const school = await this.schoolService.findOne({ where: { id: user.schoolId  } })

    const data = await prefetchFromCache(
      this.cacheManager,
      `cacheKey.findBookList.${JSON.stringify({ id })}`,
      () => this.bookListService.findBookList({ id })
    )

    // const data = await this.bookListService.findBookList({ id })
    const onlineBooks = data.books?.filter(
      (item) => item.status === OnlineOfflineStatus.ONLINE
    )
    let books = user.isTeacher
      ? onlineBooks
      : onlineBooks.filter((item) => !item.hiddeSchoolIds.includes(user.schoolId))
    books = books.map((item) => {
      return {
        ...item,
        isHidden: getBookIsHidden(item, school, user.isTeacher),
      }
    })
    return getBookListDto({ ...data, books: this.filterBookLevel(books, user) })
  }

  @ApiOperation({ summary: 'search book list' })
  @ClientAuth()
  @ApiBaseResult(BookListDto, 200)
  @Get()
  async searchBookList(@Query() query: ClientQueryBookListDto, @CurrentUser() user: any) {
    const { pageIndex = 1, pageSize = 10 } = query

    const data = await prefetchFromCache(
      this.cacheManager,
      `cacheKey.findBookList.${JSON.stringify({ url: query.url })}`,
      () => this.bookListService.findBookList({ url: query.url })
    )

    // const data = await this.bookListService.findBookList({ url: query.url })

    const school = await prefetchFromCache(
      this.cacheManager,
      `cacheKey.findOneSchool.${user.schoolId}`,
      () => this.schoolService.findOne({ where: { id: user.schoolId  } })
    )
    const shelf = await this.bookshelfService.findBookShelf(
      data.books.map((item) => item.id),
      user.userId,
      EBookVersion.SUBSCRIPTION
    )
    // const school = await this.schoolService.findOne({ where: { id: user.schoolId  } })
    const onlineBooks = data.books?.filter(
      (item) => item.status === OnlineOfflineStatus.ONLINE
    )
    let books = user.isTeacher
      ? onlineBooks
      : onlineBooks.filter((item) => !item.hiddeSchoolIds.includes(user.schoolId))
    books = books.map((item) => {
      return {
        ...item,
        isOnShelf: shelf.includes(item.id),
        isHidden: getBookIsHidden(item, school, user.isTeacher),
      }
    })
    books = this.filterBookLevel(books, user)
    // 分页处理
    const total = books.length
    const startIndex = (pageIndex - 1) * pageSize
    const endIndex = pageIndex * pageSize
    const paginatedBooks = books.slice(startIndex, endIndex)
    return getBookListDto({
      ...data,
      books: paginatedBooks,
      total,
      pageIndex,
      pageSize,
    })
  }

  private filterBookLevel(books: Book[], user: any) {
    let levels
    if (user.isTeacher) {
      levels =
        user.isAllLevelForStaff ||
        R.isNil(user.staffLevelIds) ||
        user.staffLevelIds.length === 0
          ? undefined
          : user.staffLevelIds
    } else {
      levels =
        user.isAllLevelForStudent || R.isNil(user.levels) || user.levels.length === 0
          ? undefined
          : user.levels
    }

    return levels
      ? books.filter(
        (book) =>
          R.intersection(book.bookLevels?.map((l) => l.id) ?? [], levels).length
      )
      : books
  }
}
