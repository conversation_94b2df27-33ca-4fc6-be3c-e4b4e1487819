import { Module } from '@nestjs/common'
import { IFileTemplateService, IAdminRepo, ISchoolAdminRepo, IUserRepo, IUserService } from '@/modules/shared/interfaces'
import {
  Acl<PERSON>dmin<PERSON>ontroller, Administrator<PERSON>dminController, AdministratorPublicController, 
  RegionController, SchoolAdminAdminController, SchoolAdministratorPublicController, 
  SchoolAdministratorSchoolAdminController, SchoolAdministratorSchoolSuperController, 
  SchoolRoleSchoolAdminController, UserAdminController, UserClientController, 
  UserPublicController, UserSchoolAdminController,
} from './controllers'
import { AppleProvider, GoogleProvider } from './providers'
import {
  AdminRepository,
  SchoolAdminRepository,
  UserRepository,
} from './repositories'
import {
  AclService,
  AdministratorService,
  FileTemplateService,
  SchoolAdministratorService,
  SchoolRoleService,
  TokenService,
  UserService,
} from './services'
import { TicketUtil, TwilioUtil, VerificationCodeUtil } from './utils'


@Module({
  providers: [
    // Interface implementations
    { provide: IAdminRepo, useClass: AdminRepository },
    { provide: IFileTemplateService, useClass: FileTemplateService },
    { provide: ISchoolAdminRepo, useClass: SchoolAdminRepository },
    { provide: IUserRepo, useClass: UserRepository },
    { provide: IUserService, useClass: UserService },

    // Repositories
    UserRepository,
    AdminRepository,
    SchoolAdminRepository,
    
    // Services
    UserService,
    AdministratorService,
    SchoolAdministratorService,
    TokenService,
    AclService,
    SchoolRoleService,
    FileTemplateService,

    // Providers
    GoogleProvider,
    AppleProvider,
    
    // Utils
    TicketUtil,
    TwilioUtil,
    VerificationCodeUtil,
  ],
  exports: [
    IAdminRepo,
    IFileTemplateService,
    ISchoolAdminRepo,
    IUserRepo,
    IUserService,

    AdminRepository,
    SchoolAdminRepository,
    AdministratorService,
    SchoolAdministratorService,
    TokenService,
    AclService,
    SchoolRoleService,
    FileTemplateService,
    GoogleProvider,
    AppleProvider,
    TicketUtil,
    TwilioUtil,
    VerificationCodeUtil,
  ],
  controllers: [
    // User Controllers
    UserAdminController,
    UserClientController,
    UserPublicController,
    UserSchoolAdminController,

    // Administrator Controllers
    AdministratorAdminController,
    AdministratorPublicController,

    // School Administrator Controllers
    SchoolAdminAdminController,
    SchoolAdministratorPublicController,
    SchoolAdministratorSchoolAdminController,
    SchoolAdministratorSchoolSuperController,

    // ACL & Role Controllers
    AclAdminController,
    SchoolRoleSchoolAdminController,

    // Region Controller
    RegionController,
  ],

})
export class AccountModule {}
