import { <PERSON>, Get, Header, Query, Res } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import AdmZip from 'adm-zip'
import { Response } from 'express'
import { readFileSync } from 'fs'
import moment from 'moment-timezone'
import fetch from 'node-fetch'
import R from 'ramda'
import sharp from 'sharp'
import {
  AdminAuth,
  ApiBaseResult,
  ApiListResult,
  BooleanResponse,
  CurrentAdmin,
  CurrentLocale,
  CurrentLocaleHeader,
  ELocaleType,
  ExcelService,
} from '@/common'
import { EDataExportType, ETaskType, TaskService } from '@/common/components/task'
import { IUserRepo } from '@/modules/shared/interfaces'
import { SJRC_LOGO_JPG } from '@/modules/constants'
import { HomepageService } from '@/modules/schools/services/homepage.service'
import { LogService } from '@/modules/system'
import { getDays, regionSchoolSpecification } from '@/utils'
import { getISBN } from '@/utils/book.utitl'
import { EBookVersion, SchoolType } from '../../../enums'
import {
  AdminExportStudentReadingTimeDto,
  AdminQueryStudentReadingTimeDto,
  ExportStudentReadingTimeDto,
  GetGlobalSummaryRequest,
  GetGlobalSummaryResponse,
  QueryReadingTimeAndUserDto,
  QueryReadingTimeCountDto,
  QuerySchoolPublisherReadingDto,
  QueryStudentReadingTimeDto,
  ReadingTimeAndUserDto,
  ReadingTimeCountDto,
} from '../../books/dto'
import { QueryReadingTime, QueryReadingTimeAndUser } from '../../books/interfaces'
import { CategoryService } from '../../books/services'
import {
  IBookRepo,
  IReadRecordService,
  IReferenceReadService,
} from '@/modules/shared/interfaces'
import {
  getAuthorNameZh,
  getStudentReadByYearPortExcel,
  getStudentReadPortExcel,
} from '../../books/utils/bookExcel.util'
import { SchoolService } from '../services'
import { SchoolDashboardService } from '../services/index1'

@ApiTags('Reading-time')
@Controller('v1/admin/reading-time')
@ApiExtraModels(GetGlobalSummaryResponse)
export class ReadingTimeAdminController {
  constructor(
    private readonly readRecordService: IReadRecordService,
    private readonly categoryService: CategoryService,
    private readonly bookRepository: IBookRepo,
    private readonly schoolService: SchoolService,
    private readonly excelService: ExcelService,
    private readonly referenceReadService: IReferenceReadService,
    private readonly userRepository: IUserRepo,
    private readonly taskService: TaskService,
    private readonly homepageService: HomepageService,
    private readonly logService: LogService,
    private readonly schoolDashboardService: SchoolDashboardService,
  ) {}

  @ApiOperation({ summary: 'Global summary' })
  @ApiBaseResult(GetGlobalSummaryResponse, 200)
  @AdminAuth()
  @Get('global-summary')
  async getGlobalSummary(@Query() query: GetGlobalSummaryRequest) {
    return this.readRecordService.getGlobalSummary(query)
  }

  @ApiOperation({ summary: 'count reading time by date' })
  @ApiListResult(ReadingTimeCountDto, 200)
  @AdminAuth()
  @Get('by-date')
  async queryReadingTimeCount(@Query() query: QueryReadingTimeCountDto) {
    const filter: QueryReadingTime = R.pick(
      ['bookId', 'authorId', 'publisherId', 'schoolId', 'startTime', 'endTime'],
      query
    )
    if (query.categoryId) {
      const category = await this.categoryService.getCategory({ id: query.categoryId })

      const books = await this.bookRepository.searchBooks({ categoryIds: [category.id] })
      if (books.length) {filter.bookIds = books.map((item) => item.id)}
    }

    if (query.labelId) {
      const books = await this.bookRepository.searchBooks({ labelId: [query.labelId] })
      if (books.length) {filter.bookIds = books.map((item) => item.id)}
    }

    if (query.level) {
      const books = await this.bookRepository.searchBooks({ level: [query.level] })
      if (books.length) {filter.bookIds = books.map((item) => item.id)}
    }

    const data =
      (query.labelId || query.categoryId || query.level) &&
      (!filter.bookIds || filter.bookIds.length === 0)
        ? []
        : await this.readRecordService.readingCount(filter)

    return getDays(query.startTime, query.endTime).map((date) => ({
      date,
      totalReadingTime:
        data.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
        )?.totalReadingTime ?? 0,
      totalUser:
        data.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
        )?.totalUser ?? 0,
    }))
  }

  @ApiOperation({ summary: 'count reading time and user' })
  @ApiListResult(ReadingTimeAndUserDto, 200)
  @AdminAuth()
  @Get('time-and-user')
  async queryReadingTimeAndUser(@Query() query: QueryReadingTimeAndUserDto) {
    const filter: QueryReadingTimeAndUser = R.pick(
      ['bookId', 'authorId', 'publisherId'],
      query
    )
    let count = 0
    if (query.categoryId) {
      const category = await this.categoryService.getCategory({ id: query.categoryId })
      // const categoryIds = category.children?.length
      //   ? category.children.map((item) => item.id)
      //   : [category.id]
      const books = await this.bookRepository.searchBooks({ categoryIds: [category.id] })
      count = books.length
      if (books.length) {filter.bookIds = books.map((item) => item.id)}
    }

    if (query.labelId) {
      const books = await this.bookRepository.searchBooks({ labelId: [query.labelId] })
      count = books.length
      if (books.length) {filter.bookIds = books.map((item) => item.id)}
    }

    if (query.authorId || query.publisherId) {
      count = await this.bookRepository.countBooks(
        R.pick(['authorId', 'publisherId'], query)
      )
    }
    const data =
      (query.labelId || query.categoryId) && filter.bookIds.length === 0
        ? { totalReadingTime: 0, totalUser: 0 }
        : await this.readRecordService.readingTimeAndUser(filter)

    return { ...data, count }
  }

  @AdminAuth()
  @Get('/export-school-publish-reading')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=Books.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export books' })
  async exportSchoolPublishReading(
    @Query() query: QuerySchoolPublisherReadingDto,
    @Res() res: Response,
    @CurrentLocale() local: ELocaleType = ELocaleType.ZH_HK,
    @CurrentAdmin() user: any
  ) {
    const data = await this.readRecordService.schoolPublisherReading(query)
    const schoolIds = data.map((item) => item.schoolId)
    const schools = schoolIds.length
      ? await this.schoolService.findSchools(schoolIds)
      : []

    const regions = [
      ...new Set(schools.map((item) => item.region).filter((item) => !!item)),
    ]
    if (regions.length === 0) {regions.push('HK')}
    const isAdmin = !user.publisherIds.length
    // const isAdmin = true
    let excelData = data.map((item) => {
      const school = schools.find((s) => s.id === item.schoolId)
      const schoolName = school?.name?.zh_HK ?? ''
      const region = school?.region ?? 'HK'
      return {
        startTime: moment
          .tz(query.startTime * 1000, 'Asia/Hong_Kong')
          .format('YYYY-MM-DD'),
        endTime: moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
        schoolName,
        [region]: `${Number(item.totalReadingTime) / 3600}`,
        readingTime: `${Number(item.totalReadingTime) / 3600}`,
        readingTimes: item.totalReadingTime ?? 0,
      }
    })

    const noReadingSchools = await this.schoolService.findSchoolsWithExcludedIds(
      schoolIds
    )
    excelData = (excelData || []).concat(
      noReadingSchools.map((x) => {
        const schoolName = x?.name?.zh_HK ?? ''
        const region = x?.region ?? 'HK'
        return {
          startTime: moment
            .tz(query.startTime * 1000, 'Asia/Hong_Kong')
            .format('YYYY-MM-DD'),
          endTime: moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
          schoolName,
          [region]: 0,
          readingTime: 0,
          readingTimes: 0,
        }
      })
    )
    const file = await this.excelService.buildRegionExcel({
      name: `schoolPublisherReadingTime.zh_HK`,
      specification: regionSchoolSpecification(regions, isAdmin, local),
      data: excelData,
    })

    await this.logService.save('下载订阅版出版社详情学校报告', user, query)
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('export/top-10-books')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=top 10 books.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export top 10 books' })
  async exportTop10Books(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentAdmin() user: any,
    @Query() query: any,
    @Res() res: Response
  ) {
    const schoolId = Number(query.schoolId)
    const data = await this.homepageService.getTop10Books(schoolId, {
      fields: ['id', 'isbn', 'name', 'bookId'],
      relations: ['publisher', 'authors'],
    })
    const ids = data.map((item) => item.bookId)

    const counts = ids.length
      ? await this.readRecordService.countBookReadingUsers(schoolId, ids)
      : []
    const file = await this.excelService.buildExcel({
      name: `top10Books.${local}`,
      data: data.map((item) => ({
        type: local === ELocaleType.ZH_HK ? '全部' : 'ALL',

        isbn: getISBN(item.book.isbn),
        bookName: item.book.name.zh_HK,
        author: item.book.authors?.map((item) => getAuthorNameZh(item.name)).join(','),
        readingUsers:
          counts.find((count) => count.bookId === item.bookId)?.totalUser ?? 0,
        readingTime: Number(item.totalReadingTime) / 3600,
        publisher: item.book.publisher?.name.zh_HK ?? '',
      })),
    })
    await this.logService.save('下载订阅版学校閱讀時數 TOP 10', user)
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('export/students')
  @CurrentLocaleHeader()
  @ApiBaseResult(BooleanResponse, 200, 'Export student reading time report')
  @ApiOperation({ summary: 'export student reading' })
  async exportStudents(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentAdmin() user: any,
    @Query() query: AdminQueryStudentReadingTimeDto
  ) {
    await this.taskService.deliver(
      ETaskType.DATA_EXPORT,
      {
        local,
        user,
        query,
        schoolId: query.schoolId,
        type: EDataExportType.EXPORT_SCHOOL_READING_TIME_FROM_PLATFORM_ADMIN,
      },
      {}
    )
    return {
      status: true,
    }
  }

  @AdminAuth()
  @Get('export/students/all-school')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '导出全平台阅读时数' })
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=students-reading-time.xlsx')
  async exportStudentsAllSchool(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentAdmin() user: any,
    @Query() query: ExportStudentReadingTimeDto,
    @Res() res: Response
  ) {
    const schools = await this.schoolService.listAllSchool()
    const file = await this.schoolDashboardService.exportReadingOfStudentAllSchool({
      local,
      schools,
      query,
      user,
    })
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('export/students/read-report')
  @Header('Content-Type', 'application/zip')
  @Header('Content-Disposition', 'attachment; filename=students.zip')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export student reading' })
  async exportStudentsReadReport(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentAdmin() user: any,
    @Query() query: AdminExportStudentReadingTimeDto,
    @Res() res: Response
  ) {
    let readPeriod = ''
    if (query.startTime && query.endTime) {
      readPeriod =
        moment.tz(query.startTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD') +
        ' - ' +
        moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD')
    }

    const version = query.version ?? EBookVersion.SUBSCRIPTION
    const students = await this.getReadInfoOfStudent(query, user, version)
    const school = await this.schoolService.findOne({ where: { id: query.schoolId  } })
    const logoImage = await fetch(SJRC_LOGO_JPG)
    const logoBuffer = Buffer.from(await logoImage.arrayBuffer())
    const logoResize = await sharp(logoBuffer).resize(300).png({ quality: 90 }) //压缩图片
    const logoResizeBuffer = await logoResize.toBuffer()
    let schoolLogoResizeBuffer = null
    let schoolLogoPictureSpacing = 0
    if (school.logo) {
      const schoolLogoImage = await fetch(school.logo)
      const schoolLogoBuffer = Buffer.from(await schoolLogoImage.arrayBuffer())
      const schoolLogResize = await sharp(schoolLogoBuffer)
        .resize(300)
        .png({ quality: 90 }) //压缩图片
      schoolLogoResizeBuffer = await schoolLogResize.toBuffer()
      const { width, height } = await schoolLogResize.metadata()
      const proportion = width / height //计算logo的宽高比
      schoolLogoPictureSpacing = 37 * proportion //计算 logo在表格的间距
    }
    const zip = new AdmZip()
    for (const student of students) {
      const { books, schoolName, studentName } = student
      if (books?.length === 0) {continue}
      Object.assign(student, {
        version,
        schoolName: schoolName?.zh_HK,
        schoolLogoBuffer: schoolLogoResizeBuffer,
        logoBuffer: logoResizeBuffer,
        readPeriod,
        readTotal: books?.length,
        principalName: query.principalName,
        librarianName: query.librarianName,
        gradeClass: `${student.grade}/${student.class}`,
        schoolLogoPictureSpacing,
      })
      books.map((book) => {
        const {
          name,
          total_reading_time,
          total_reading_count,
          school_year,
          old_grade,
          old_class,
        } = book
        Object.assign(book, {
          name: name?.zh_HK,
          time: Number(total_reading_time) ?? 0,
          count: Number(total_reading_count) ?? 0,
          school_year,
          old_grade_class: `${old_grade}/${old_class}`,
        })
      })
      let wb
      if (query.byYear) {
        wb = getStudentReadByYearPortExcel(student)
      } else {
        wb = getStudentReadPortExcel(student)
      }
      const studentBuffer = await wb.writeToBuffer()
      zip.addFile(`${studentName}.xlsx`, studentBuffer)
    }

    await this.logService.save('下载订阅版閱讀詳情（用戶）阅读报表', user, query)
    res.send(zip.toBuffer())
  }

  @AdminAuth()
  @Get('export/books')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=books.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export books reading' })
  async exportReadingBooks(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentAdmin() user: any,
    @Query() query,
    @Res() res: Response
  ) {
    const data = await this.readRecordService.listStudentReadingByBook(
      {
        startTime: query.startTime,
        endTime: query.endTime,
      },
      query.schoolId
    )

    const books = await this.bookRepository.searchBooks(
      { ids: data.items.map((item) => item.bookId) },
      { withDeleted: true },
      {
        fields: ['id', 'name', 'isbn'],
        relations: ['authors', 'publisher'],
      }
    )

    const file = await this.excelService.buildExcel({
      name: `readingOfBooks.${local}`,
      data: data.items.map((item) => {
        const book = books.find((book) => book.id === item.bookId)
        return {
          startTime: moment
            .tz(query.startTime * 1000, 'Asia/Hong_Kong')
            .format('YYYY-MM-DD'),
          endTime: moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
          isbn: getISBN(book.isbn) ?? '',
          bookName: book.name?.zh_HK,
          author: book.authors?.map((item) => getAuthorNameZh(item.name)).join(',') ?? '',
          publisher: item.publisherName?.zh_HK,
          totalUser: item.totalUser ?? 0,
          totalReadingTime: Number(item.totalReadingTime) / 3600 ?? 0,
          totalReadingTimes: item.totalReadingTime ?? 0,
        }
      }),
    })
    await this.logService.save('下载订阅版閱讀詳情（書籍）', user, query)
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('export/books/test')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=books.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export books reading' })
  async exportReadingBooksTest(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentAdmin() user: any,
    @Query() query,
    @Res() res: Response
  ) {
    const dataStr = await readFileSync('../stats.json', { encoding: 'utf-8' })
    const data = JSON.parse(dataStr)
    // data = data.slice(0, 5)
    // console.log(data)

    const sheetConfigName = 'readingOfBooksV2'
    const file = await this.excelService.buildExcel({
      name: `${sheetConfigName}.${local}`,
      data: data.map((item) => {
        return {
          schoolType: convertSchoolTypeToName(item.type),
          schoolName: item.schoolName,
          startTime: '2024-02-01',
          endTime: '2024-06-30',
          isbn: item.isbn,
          bookName: item.bookName,
          author: item.authorsName,
          publisher: item.publisher_name,
          totalUser: item.totalUser ?? 0,
          totalReadingTime: Number(item.totalReadingTime) / 3600 ?? 0,
          totalReadingTimes: item.totalReadingTime ?? 0,
        }
      }),
    })
    await this.logService.save('下载订阅版閱讀詳情（書籍）', user, query)
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('export/reading-time-in-class')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=reading time in class.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export reading time in class' })
  async exportReadingTimeInClass(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentAdmin() user: any,
    @Query() query,
    @Res() res: Response
  ) {
    const file = await this.schoolDashboardService.exportReadingTimeByClass(
      query,
      query,
      local
    )
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('export/user-count-in-class')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=reading user count in class.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export reading user count in class' })
  async exportReadingUserCountInClass(
    @CurrentLocale() locale: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @CurrentAdmin() user: any,
    @Query() query
  ) {
    console.log(query)
    const file = await this.schoolDashboardService.exportReadingUserCountByClass(
      query,
      query,
      locale
    )

    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('export/user-count-in-day')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header(
    'Content-Disposition',
    'attachment; filename=reading time and count by date.xlsx'
  )
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export reading time and user count by date' })
  async exportReadingUserCountByDay(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    // @CurrentSchoolAdmin() user: any,
    @Query() query
  ) {
    const data = await this.schoolDashboardService.readingUserCountAndTimeByDay(
      query.schoolId,
      query
    )

    const file = await this.excelService.buildExcel({
      name: `readingTimeAndCountByDate.${local}`,
      data: data.map((item) => ({
        date: item.date,
        totalUser: item.count,
        totalReadingTime: Number(item.totalReadingTime) / 3600,
      })),
    })
    res.send(Buffer.from(file))
  }

  private async getReadInfoOfStudent(
    query: any,
    schoolAdmin: any,
    version: EBookVersion
  ) {
    console.log(version)
    const { schoolId } = query
    const readingService =
      version === EBookVersion.SUBSCRIPTION
        ? this.readRecordService
        : this.referenceReadService
    let userIds = []
    if (query.keyword) {
      const accounts = await this.userRepository.getUsers(schoolId, query.keyword)
      userIds = accounts.map((item) => item.id)
      if (userIds.length === 0) {
        return {
          items: [],
          total: 0,
          pageIndex: query.pageIndex,
          pageSize: query.pageSize,
        }
      }
    } else {
      userIds = await readingService.getReadingUserIdsBySchool(
        schoolAdmin.schoolId,
        query
      )
    }
    const students = await this.userRepository.getUsersSchoolGradeClass(
      schoolId,
      query,
      userIds
    )
    for (const student of students) {
      const books = await readingService.listReadBooks(student.id, schoolId, query)
      Object.assign(student, { books })
    }
    return students
  }
}

export function convertSchoolTypeToName(type?: SchoolType) {
  switch (type) {
    case SchoolType.COLLEGES:
      return '大專院校'
    case SchoolType.KINDERGARTEN:
      return '幼稚園'
    case SchoolType.MIDDLE:
      return '中學'
    case SchoolType.PRIMARY:
      return '小學'
    default:
      return ''
  }
}
