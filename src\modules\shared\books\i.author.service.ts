import { CreateAuthorDto } from '@/modules/books/dto'
import {Author} from '@/entities'

interface IAuthor {

}

export abstract class IAuthorService implements IAuthor {
    abstract searchAuthor(keyword: string): Promise<any>
    abstract getAuthor(id: number): Promise<any>
    abstract createAuthor(data: CreateAuthorDto, admin: any): Promise<any>
    abstract exportAuthors(options: any): Promise<any>

    abstract  findAuthorNames(bookIds: number[])

    abstract getAuthors(ids: number[], withDeleted?: Boolean ): Promise<Author[]>
}