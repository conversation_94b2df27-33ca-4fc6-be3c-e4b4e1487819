import { <PERSON>, Get, Header, Query, Res } from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'
import { Response } from 'express'
import moment from 'moment-timezone'
import R from 'ramda'
import {
  ApiListResult,
  CurrentLocale,
  CurrentLocaleHeader,
  CurrentSchoolAdmin,
  ELocaleType,
  ExcelService,
  SchoolAdminAuth,
} from '@/common'
import { PAGE_SIZE } from '@/modules/constants'
import { getISBN, getName } from '@/utils/book.utitl'
import { LimitDto, QueryReadingTimeDto, ReferenceTopBookDto } from '../../books/dto'
import { QuerySchoolReferenceBookStatisticDto } from '../../books/dto/referenceBook.dto'
import { IReferenceReadService } from '@/modules/shared/interfaces'
import { GradeService, HomepageService } from '../services'

@Controller('v1/school-admin/reference-read')
export class ReferenceReadSchoolController {
  constructor(
    private readonly referenceReadService: IReferenceReadService,
    private readonly gradeService: GradeService,
    private readonly homepageService: HomepageService,
    private readonly excelService: ExcelService,
  ) {}

  @ApiOperation({ summary: 'get top n books in reference' })
  @Get('top-n-books')
  @ApiListResult(ReferenceTopBookDto, 200)
  @SchoolAdminAuth()
  async getTopNBooks(@CurrentSchoolAdmin() admin: any, @Query() query: LimitDto) {
    const { limit = PAGE_SIZE } = query
    return this.referenceReadService.getTopBooks(admin.schoolId, limit)
  }

  @SchoolAdminAuth()
  @Get('export/top-10-books')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment; filename=top 10 books.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export top 10 books' })
  async exportTop10Books(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response,
  ) {
    const file = await this.homepageService.exportReferenceHot(user.schoolId, local, user)
    res.send(Buffer.from(file))
  }

  @ApiOperation({ summary: 'distribution of readers in reference' })
  @Get('users-count')
  @SchoolAdminAuth()
  async getUserCount(
    @CurrentSchoolAdmin() admin: any,
    @Query() query: QueryReadingTimeDto,
  ) {
    return this.referenceReadService.groupByClass(admin.schoolId, query)
  }

  @SchoolAdminAuth()
  @Get('users-count/export')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment; filename=reading user count.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export reading user count' })
  async exportUserCount(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @CurrentSchoolAdmin() user: any,
    @Query() query: QueryReadingTimeDto,
  ) {
    const data = await this.referenceReadService.groupByClass(user.schoolId, query)
    const students = data.students.filter((item) => item.classes.length != 0)

    const studentTotalCount = R.flatten(
      students.map((item) => item.classes.map((c) => Number(c.userCount ?? 0))),
    ).reduce((pre, time) => pre + time, 0)

    const totalCount = studentTotalCount + Number(data.teachers.userCount ?? 0)

    const excelData: any = R.flatten(
      students.map((item) => {
        const gradeCount = item.classes.reduce(
          (pre, curr) => pre + Number(curr.userCount ?? 0),
          0,
        )

        return item.classes.map((c) => {
          const classCount = Number(c.userCount ?? 0)
          return {
            startTime: moment
              .tz(query.startTime * 1000, 'Asia/Hong_Kong')
              .format('YYYY-MM-DD'),
            endTime: moment
              .tz(query.endTime * 1000, 'Asia/Hong_Kong')
              .format('YYYY-MM-DD'),
            type: local === ELocaleType.ZH_HK ? '學生' : 'STUDENT',
            grade: item.grade ?? '',
            class: c.class ?? '',
            gradeReaderCount: gradeCount,
            gradeReaderCountRatio:
              totalCount > 0 ? `${(gradeCount / totalCount) * 100}` : 0,
            classReaderCount: classCount,
            classReaderCountRatio:
              gradeCount > 0 ? `${(classCount / gradeCount) * 100}` : 0,
          }
        })
      }),
    )
    const teacherCount = Number(data.teachers.userCount ?? 0)
    excelData.push({
      startTime: moment.tz(query.startTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
      endTime: moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
      type: local === ELocaleType.ZH_HK ? '教職員' : 'TEACHER',
      gradeReaderCount: teacherCount,
      gradeReaderCountRatio:
        totalCount > 0 ? `${(Number(teacherCount) / totalCount) * 100}` : 0,
      grade: '-',
      class: '-',
      classReaderCount: '-',
      classReaderCountRatio: '-',
    })
    let row = 2
    const merges = R.flatten(
      students.map((item) => {
        const length = item.classes.length === 0 ? 1 : item.classes.length
        const merge = [
          {
            start: { row: row, column: 4 },
            end: { row: row + length - 1, column: 4 },
          },
          {
            start: { row: row, column: 5 },
            end: { row: row + length - 1, column: 5 },
          },
          {
            start: { row: row, column: 6 },
            end: { row: row + length - 1, column: 6 },
          },
        ]
        row += length
        return merge
      }),
    )
    merges.push({
      start: { row: 2, column: 1 },
      end: { row: excelData.length + 1, column: 1 },
    })
    merges.push({
      start: { row: 2, column: 2 },
      end: { row: excelData.length + 1, column: 2 },
    })
    merges.push({
      start: { row: 2, column: 3 },
      end: { row: excelData.length, column: 3 },
    })

    const file = await this.excelService.buildExcel({
      name: `schoolReferenceUserCount.${local}`,
      data: excelData,
      merges,
    })
    res.send(Buffer.from(file))
  }

  @ApiOperation({ summary: '阅读详细-用户' })
  @Get('users')
  @SchoolAdminAuth()
  async getStatistic(
    @CurrentSchoolAdmin() admin: any,
    @Query() query: QuerySchoolReferenceBookStatisticDto,
  ) {
    const data = await this.referenceReadService.statistic(admin.schoolId, query)
    const grades = await this.gradeService.listGrades(
      data.items.map((item) => item.gradeId),
    )

    return {
      ...data,
      items: data.items.map((item) => ({
        ...R.pick(['displayName', 'class', 'grade', 'bookName'], item),
        totalReadCount: Number(item.totalReadCount),
        total: Number(item.total),
        totalBookCount: Number(item.totalBookCount || 0),
        grade: grades.find((grade) => grade.id === item.gradeId)?.grade,
      })),
    }
  }

  @SchoolAdminAuth()
  @Get('users/export')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment; filename=users.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export reading user' })
  async exportUsers(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @CurrentSchoolAdmin() admin: any,
    @Query() query: QuerySchoolReferenceBookStatisticDto,
  ) {
    const data = await this.referenceReadService.exportUsers(admin.schoolId, query)
    const file = await this.excelService.buildExcel({
      name: `referenceReadingOfUsers.${local}`,
      data: data.map((item) => ({
        startTime: moment
          .tz(query.startTime * 1000, 'Asia/Hong_Kong')
          .format('YYYY-MM-DD'),
        endTime: moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
        name: item.displayName,
        isbn: getISBN(item.isbn),
        grade: item.grade,
        class: item.class,
        bookName: getName(item.bookName),
        serialNo: item.serialNo,
        email: item.email,
        publisher: getName(item.publisherName),
        counts: Number(item.total),
        bookCounts: Number(item.totalBookCount),
        totalCounts: Number(item.totalReadCount),
        ratio: Number(item.totalBookByGrade || 0)
          ? `${(Number(item.totalBookCount) / Number(item.totalBookByGrade)) * 100}`
          : 0,
      })),
    })
    res.send(Buffer.from(file))
  }
}
