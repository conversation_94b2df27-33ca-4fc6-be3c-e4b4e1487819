import { forward<PERSON><PERSON>, Module } from '@nestjs/common'
import { AccountModule } from '@/modules/account/account.module'
import {
  IAuthorService,
  IBookLevelService,
  IBookRepo,
  IBookService,
  IBookshelfService,
  IInitLeaderBoardService,
  ILeaderBoardService,
  IPublisherService,
  IReadingPosService,
  IReadRecordService,
  IReferenceReadService,
  IViewBookDetailService,
} from '@/modules/shared/interfaces'
import { SharedModule } from '@/modules/shared/shared.module'
import { BookKeywordCacheService } from '@/modules/tasks/bookKeywordCache.cron'
import {
  AuthorAdminController, AuthorClientController, AuthorSchoolController, BookAdminController, BookClientController,
  BookClientV2Controller, BookLevelAdminController, BookNoteClientController, BookSchoolAdminController, BooksFileAdminController,
  BookshelfClientController, CategoryAdminController, CategoryClientController, CategorySchoolController, FileAdminController,
  FileController, HotWordAdminController, HotWordClientController, LabelAdminController, LabelClientController,
  OperateApplicationAdminController, PublisherAdminController, PublisherClientController, PublisherSchoolController,
  RecommendWordAdminController, RecommendWordClientController, ReportAdminController, TextToSpeechController, UtilController,
} from './controllers'
import {
  BookProvider,
  EPubLocationsService,
  EpubProvider,
  PdfProvider,
} from './providers'
import {
  AuthorService,
  BookLevelService,
  BookS3Service,
  BookshelfService,
  CategoryService,
  ChapterService,
  HotSearchWordService,
  InitLeaderBoardService,
  LabelService,
  LeaderBoardService,
  PublisherService,
  ReadingPosService,
  RecommendWordService,
  ReferenceBookService,
} from './services'
import {
  BookNoteService,
  BookRepository,
  ReadRecordService,
  ReferenceReadService,
  ViewBookDetailService,
} from './services/index1'
import { BookFileService, BookService, ReportService } from './services/index3'
import {
  ParseBookTask,
  SearchWordTask,
  UpdateBookStatusTask,
  UpdateHotBookLevelTask,
} from './tasks'

@Module({

  exports: [
    // Abstract interfaces
    IAuthorService,
    IBookLevelService,
    IBookService,
    IBookshelfService,
    IReadingPosService,
    IReadRecordService,
    IReferenceReadService,
    ILeaderBoardService,
    IInitLeaderBoardService,
    IPublisherService,
    IBookRepo,
    IViewBookDetailService,

    // Concrete services
    BookRepository,
    BookService,
    BookFileService,
    ReadRecordService,
    PublisherService,
    CategoryService,
    LabelService,
    ChapterService,
    BookshelfService,
    InitLeaderBoardService,
    LeaderBoardService,
    BookLevelService,
    HotSearchWordService,
    RecommendWordService,

    ReportService,
    BookProvider,
    EpubProvider,
    PdfProvider,
  ],
  providers: [
    // Core Services
    BookRepository,
    BookService,
    BookFileService,
    BookS3Service,
    BookshelfService,
    BookNoteService,
    ReadRecordService,
    ViewBookDetailService,
    ReferenceReadService,
    ReferenceBookService,

    // Category & Classification
    CategoryService,
    LabelService,
    AuthorService,
    PublisherService,

    // Content Services
    ChapterService,

    ReportService,

    // Search & Recommendation
    HotSearchWordService,
    RecommendWordService,

    // Level & Position
    BookLevelService,
    ReadingPosService,
    InitLeaderBoardService,
    LeaderBoardService,

    // Providers
    BookProvider,
    EpubProvider,
    PdfProvider,
    EPubLocationsService,

    // Tasks
    ParseBookTask,
    SearchWordTask,
    UpdateBookStatusTask,
    UpdateHotBookLevelTask,
    BookKeywordCacheService,

    //  Services
    AuthorService,
    BookService,
    BookLevelService,
    BookshelfService,
    ReadingPosService,
    ReadRecordService,
    ReferenceReadService,

    LeaderBoardService,
    InitLeaderBoardService,
    PublisherService,
    BookRepository,

    // Interface implementations
    { provide: IAuthorService, useClass: AuthorService },
    { provide: IBookLevelService, useClass: BookLevelService },
    { provide: IBookService, useClass: BookService },
    { provide: IBookshelfService, useClass: BookshelfService },
    { provide: IReadingPosService, useClass: ReadingPosService },
    { provide: IReadRecordService, useClass: ReadRecordService },
    { provide: IReferenceReadService, useClass: ReferenceReadService },

    { provide: ILeaderBoardService, useClass: LeaderBoardService },
    { provide: IInitLeaderBoardService, useClass: InitLeaderBoardService },
    { provide: IPublisherService, useClass: PublisherService },
    { provide: IBookRepo, useClass: BookRepository },
    { provide: IViewBookDetailService, useClass: ViewBookDetailService },
  ],
  imports: [
    SharedModule,
  ],

  controllers: [
    // Admin Controllers
    BookAdminController,
    BooksFileAdminController,
    CategoryAdminController,
    LabelAdminController,
    PublisherAdminController,
    AuthorAdminController,
    FileAdminController,
    ReportAdminController,
    RecommendWordAdminController,
    HotWordAdminController,
    BookLevelAdminController,
    OperateApplicationAdminController,

    // Client Controllers
    BookClientController,
    BookClientV2Controller,
    BookshelfClientController,
    CategoryClientController,
    LabelClientController,
    PublisherClientController,
    AuthorClientController,
    BookNoteClientController,
    HotWordClientController,
    RecommendWordClientController,

    // School Controllers
    BookSchoolAdminController,
    CategorySchoolController,
    AuthorSchoolController,
    PublisherSchoolController,

    // Utility Controllers
    FileController,
    TextToSpeechController,
    UtilController,
  ],

})
export class BookModule {}
