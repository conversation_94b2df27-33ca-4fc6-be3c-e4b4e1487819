import { Body, Controller, Get, Post, Query } from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'
import {
  ApiBaseResult,
  ApiPageResult,
  BooleanResponse,
  ClientAuth,
  CurrentUser,
  PageRequest,
} from '@/common'
import { EBookVersion } from '@/enums'
import { BookIDDto, ReferenceHotBookDto } from '../../books/dto'
import { IViewBookDetailService, IBookshelfService } from '@/modules/shared/interfaces'

@Controller('v1/client/book-detail')
export class ViewBookDetailClientController {
  constructor(
    private readonly viewBookDetailService: IViewBookDetailService,
    private readonly bookshelf: IBookshelfService,
  ) {}

  @ApiOperation({ summary: 'View book detail' })
  @ApiBaseResult(BooleanResponse, 200)
  @ClientAuth()
  @Post()
  async viewBookDetail(@CurrentUser() user: any, @Body() body: BookIDDto) {
    await this.viewBookDetailService.viewBookDetail(
      body.bookId,
      user.userId,
      user.schoolId,
    )
  }

  // @ApiOperation({ summary: 'reference hot books' })
  // @ApiPageResult(ReferenceHotBookDto, 200)
  // @ClientAuth()
  // @Get('hot')
  // async listViewBookDetail(@CurrentUser() user: any, @Query() query: PageRequest) {
  //   const data = await this.viewBookDetailService.listViewBookDetail(user.schoolId, query)

  //   const shelf = await this.bookshelf.findBookShelf(
  //     data.items.map((item) => item.id),
  //     user.userId,
  //     EBookVersion.REFERENCE,
  //   )

  //   return {
  //     ...data,
  //     items: data.items.map((item) => ({ ...item, isOnShelf: shelf.includes(item.id) })),
  //   }
  // }
}
