import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { ApiConsumes, ApiExtraModels, ApiOperation, ApiQuery } from '@nestjs/swagger'
import { Express, Response } from 'express'
import R from 'ramda'
import {
  AdminAuth,
  ApiBaseResult,
  ApiFile,
  ApiPageResult,
  BooleanResponse,
  CurrentAdmin,
  CurrentLocale,
  CurrentLocaleHeader,
  ELocaleType,
  PageRequest,
} from '@/common'
import { Book, Contract } from '@/entities'
import { DecodeContractBooksFileResponse } from '@/modules/books/dto'
import { getISBN } from '@/utils/book.utitl'
import { IBookRepo } from '@/modules/shared/interfaces'
import {
  CreateContractDto,
  ListContractsRequest,
  PublishContractDto,
  QueryContractDetailDto,
  UpdateContractDto,
} from '../dto/contract.dto'
import { ContractService } from '../services'

@Controller('v1/admin')
@ApiExtraModels(Contract, BooleanResponse, DecodeContractBooksFileResponse)
export class ContractAdminController {
  constructor(
    private readonly contractService: ContractService,
    private readonly bookRepositories: IBookRepo,
  ) {}

  @ApiOperation({ summary: '创建合同' })
  @AdminAuth()
  @ApiBaseResult(Contract, 201)
  @Post('contracts')
  async createContract(@Body() data: CreateContractDto, @CurrentAdmin() admin: any) {
    const contract = await this.contractService.createContract(data, { admin })

    const books = await this.bookRepositories.listBooks({
      ids: contract.contractBooks.map((contractBook) => contractBook.book.id),
    })

    return {
      ...contract,
      contractBooks: contract.contractBooks.map((contractBook) => {
        const book = R.pick(
          ['id', 'name', 'isbn', 'authors', 'coverUrl'],
          books.find((b) => b.id === contractBook.book.id) || {},
        ) as Book
        return {
          ...book,
          isbn: getISBN(book.isbn),
          copiesCount: contractBook.copiesCount,
        }
      }),
    }
  }

  @ApiOperation({ summary: '更新合同' })
  @AdminAuth()
  @ApiBaseResult(Contract, 201)
  @Patch('contracts/:id')
  async patchContract(
    @Param('id') id: string,
    @Body() data: UpdateContractDto,
    @CurrentAdmin() admin: any,
  ) {
    return this.contractService.updateContract(id, data, { admin })
  }

  @ApiOperation({ summary: '发布合同' })
  @AdminAuth()
  @ApiBaseResult(BooleanResponse, 201)
  @Post('contracts/publish')
  async publishContract(@Body() data: PublishContractDto, @CurrentAdmin() admin: any) {
    await this.contractService.publishVersion(data, { admin })
    return { status: true }
  }

  @ApiOperation({ summary: '获取合同详情' })
  @AdminAuth()
  @CurrentLocaleHeader()
  @ApiBaseResult(Contract, 201)
  @Get('contracts/:id')
  async getContract(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: QueryContractDetailDto,
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
  ) {
    return this.contractService.getContract(id, query.keyword)
  }

  @ApiOperation({ summary: '获取合同列表' })
  @AdminAuth()
  @CurrentLocaleHeader()
  @ApiPageResult(Contract, 201)
  @Get('contracts')
  async listContract(@Query() query: ListContractsRequest) {
    return this.contractService.listContracts(query)
  }

  // @ApiOperation({ summary: '获取当前合同草稿' })
  // @AdminAuth()
  // @ApiBaseResult(Contract, 201)
  // @ApiQuery({
  //   name: 'schoolId',
  //   type: String,
  //   required: true,
  // })
  // @CurrentLocaleHeader()
  // @Get('draft-contracts/:schoolId')
  // async getDraftContract(
  //   @Param('schoolId', ParseIntPipe) schoolId: number,
  //   @Query() query: PageRequest,
  //   @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
  // ) {
  //   return this.contractService.getDraftContract(schoolId, query)
  // }

  @AdminAuth()
  @Get('/contract-books-file/example')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'Download template file' })
  async downloadTemplateFile(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
  ) {
    const file = await this.contractService.downloadTemplateFile(local)
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    )
    res.setHeader('Content-Disposition', 'attachment; filename=TemplateFile.xlsx')

    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @ApiBaseResult(DecodeContractBooksFileResponse, 200, 'Decode uploaded file content')
  @ApiOperation({ summary: 'Decode uploaded file content' })
  @Post('/contract-books-file/decode')
  @ApiConsumes('multipart/form-data')
  @ApiFile('file')
  @CurrentLocaleHeader()
  @UseInterceptors(FileInterceptor('file'))
  async decodeContractBooksFile(
    @UploadedFile() file: Express.Multer.File,
    @CurrentLocale() local: ELocaleType = ELocaleType.ZH_HK,
  ): Promise<any> {
    const { errors, books } = await this.contractService.uploadBookTemplateFile(
      file,
      local,
    )
    return { errors: errors?.length ? errors : undefined, books }
  }
}
