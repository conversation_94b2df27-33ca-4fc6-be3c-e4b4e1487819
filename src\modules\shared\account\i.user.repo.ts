import { DistributionTimeToSearchUserDto } from '@/modules/schools/dto'
import {SearchUserBalanceDto} from '@/modules/account'

interface IUser{
    findOne(options: any): Promise<any>
    find(options: any): Promise<any>
    createUser(schoolId: number, data: any): Promise<any>
    updateUser(id: number, data: any): Promise<any>
    count(options: any): Promise<number>
    findUsers(options: any): Promise<any>
    getUsers(schoolId: number, keyword?: string): Promise<any>
    findUsersWithDelete(options: any): Promise<any>
    getUsersSchoolGradeClass(schoolId: number, query: any, userIds?: number[]): Promise<any>
    searchUserIds(schoolId?: number, options?: DistributionTimeToSearchUserDto): Promise<any>
    countByDay(options: any): Promise<any>
    findUserWithCache(userId: number): Promise<any>
    updateUsers(userIds: number[], data: any): Promise<any>
}

export abstract class IUserRepo implements IUser {
    abstract findOne(options: any): Promise<any>
    abstract find(options: any): Promise<any>
    abstract createUser(schoolId: number, data: any): Promise<any>
    abstract updateUser(id: number, data: any): Promise<any>
    abstract count(options: any): Promise<number>
    abstract findUsers(options: any): Promise<any>
    abstract getUsers(schoolId: number, keyword?: string): Promise<any>
    abstract findUsersWithDelete(options: any): Promise<any>
    abstract getUsersSchoolGradeClass(schoolId: number, query: any, userIds?: number[]): Promise<any>
    abstract searchUserIds(schoolId?: number, options?: DistributionTimeToSearchUserDto): Promise<any>
    abstract countByDay(options: any): Promise<any>
    abstract findUserWithCache(userId: number): Promise<any>
    abstract updateUsers(userIds: number[], data: any): Promise<any>

    abstract searchUserBalances(schoolId?: number, options?: SearchUserBalanceDto)
}