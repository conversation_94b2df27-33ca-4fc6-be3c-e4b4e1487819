import {EUserType} from '@/enums'

interface ILeaderBoard {
  removeBooksFromReference(bookIds: number[], schoolIds: number[]): Promise<any>
  removeBooksForReadingRanking(bookIds: number[], schoolIds: number[], userType: any): Promise<any>

  increaseReadingTime(
    schoolId: number,
    readingTime: number,
    bookId: number,
    isHidden: boolean
  )

  getTotalOfReadingRanking(schoolId: number, type: EUserType)

  getTotalOfReferenceRanking(schoolId: number)

  getTopReferenceRanking(schoolId: number, start: number, end: number)

  getTopReadingRanking(
    schoolId: number,
    type: EUserType,
    start: number,
    end: number
  )
}

export abstract class ILeaderBoardService implements ILeaderBoard {
  abstract removeBooksFromReference(bookIds: number[], schoolIds: number[]): Promise<any>
  abstract removeBooksForReadingRanking(bookIds: number[], schoolIds: number[], userType: any): Promise<any>
  abstract increaseReadingTime(
    schoolId: number,
    readingTime: number,
    bookId: number,
    isHidden: boolean
  ): Promise<any>
  abstract getTotalOfReadingRanking(schoolId: number, type: EUserType): Promise<any>
  abstract getTotalOfReferenceRanking(schoolId: number): Promise<any>
  abstract getTopReferenceRanking(schoolId: number, start: number, end: number): Promise<any>
  abstract getTopReadingRanking(schoolId: number, type: EUserType, start: number, end: number): Promise<any>

  abstract increaseReferenceCount(schoolId: number, bookId: number)
}