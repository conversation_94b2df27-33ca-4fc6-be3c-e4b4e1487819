import { Controller, Get, Header, Query, Res } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { InjectRepository } from '@nestjs/typeorm'
import AdmZip from 'adm-zip'
import { Response } from 'express'
import moment from 'moment-timezone'
import fetch from 'node-fetch'
import R from 'ramda'
import sharp from 'sharp'
import { Repository } from 'typeorm'
import {
  ApiBaseResult,
  ApiListResult,
  ApiPageResult,
  BooleanResponse,
  CurrentLocale,
  CurrentLocaleHeader,
  CurrentSchoolAdmin,
  ELocaleType,
  ExcelService,
  SchoolAdminAuth,
} from '@/common'
import { EDataExportType, ETaskType, TaskService } from '@/common/components/task'
import { School } from '@/entities'
import { EBookVersion, SchoolType } from '@/enums'
import { PAGE_SIZE, SJRC_LOGO_JPG } from '@/modules/constants'
import {  IUserRepo, IReadRecordService, IReferenceReadService,  } from '@/modules/shared/interfaces'
import { LogService } from '@/modules/system'
import { getISBN } from '@/utils/book.utitl'
import {
  BookReadingTimeDto,
  ExportStudentReadingTimeDto,
  QueryReadingTimeDto,
  QueryReadingTimeOfStudentByBookDto,
  QueryStudentReadingTimeDto,
  ReadingTimeUserCountDto,
  SchoolReadingTimeCountDto,
  SchoolReadingUserCountDto,
  SchoolUserCountByDateDto,
  StudentReadingTimeDto,
  Top10BookOfReadingTime,
} from '../../books/dto'
import {
  getAuthorNameZh,
  getStudentReadByYearPortExcel,
  getStudentReadPortExcel,
} from '../../books/utils/bookExcel.util'
import { SchoolService, HomepageService } from '../services'
import { SchoolDashboardService } from '../services/index1'
import { convertSchoolTypeToName } from './readingTime.admin.controller'

@ApiTags('school-dashboard')
@ApiExtraModels(StudentReadingTimeDto, SchoolReadingUserCountDto, StudentReadingTimeDto)
@Controller('v1/school-admin/reading-time')
export class ReadingTimeSchoolController {
  constructor(
    @InjectRepository(School)
    private readonly schoolRepository: Repository<School>,
    private readonly referenceReadService: IReferenceReadService,
    private readonly readRecordService: IReadRecordService,
    private readonly userRepository: IUserRepo,
    private readonly excelService: ExcelService,
    private readonly schoolService: SchoolService,
    private readonly taskService: TaskService,
    private readonly homepageService: HomepageService,
    private readonly logService: LogService,
    private readonly schoolDashboardService: SchoolDashboardService
  ) {}

  @ApiOperation({ summary: 'get reading user count by day' })
  @ApiListResult(SchoolUserCountByDateDto, 200)
  @SchoolAdminAuth()
  @Get('user-count-in-day')
  async readingUserCountByDay(
    @CurrentSchoolAdmin() user: any,
    @Query() query: QueryReadingTimeDto
  ) {
    return this.schoolDashboardService.readingUserCountAndTimeByDay(user, query)
  }

  @ApiOperation({ summary: 'get reading user count by grade and class' })
  @ApiBaseResult(SchoolReadingUserCountDto, 200)
  @SchoolAdminAuth()
  @Get('user-count-in-class')
  async readingUserCount(
    @CurrentSchoolAdmin() user: any,
    @Query() query: QueryReadingTimeDto
  ) {
    return this.schoolDashboardService.getReadingUserCountByClass(user, query)
  }

  @ApiOperation({ summary: 'reading time statistic' })
  @ApiBaseResult(SchoolReadingTimeCountDto, 200)
  @SchoolAdminAuth()
  @Get('reading-time-in-class')
  async statisticReadingTime(
    @CurrentSchoolAdmin() user: any,
    @Query() query: QueryReadingTimeDto
  ) {
    return this.schoolDashboardService.getReadingTimeByClass(user, query)
  }

  @ApiOperation({ summary: `get detail of student's reading time` })
  @ApiPageResult(StudentReadingTimeDto, 200)
  @SchoolAdminAuth()
  @Get('students')
  async studentReadingTime(
    @CurrentSchoolAdmin() user: any,
    @Query() query: QueryStudentReadingTimeDto
  ) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    return this.schoolDashboardService.getReadingOfStudent(
      { ...query, pageIndex, pageSize },
      user.schoolId
    )
  }

  @ApiOperation({ summary: `get detail of book's reading time` })
  @ApiPageResult(BookReadingTimeDto, 200)
  @SchoolAdminAuth()
  @Get('books')
  async bookReadingTime(
    @CurrentSchoolAdmin() user: any,
    @Query() query: QueryReadingTimeOfStudentByBookDto
  ) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    return this.schoolDashboardService.listStudentReadingByBook(
      {
        ...query,
        pageIndex,
        pageSize,
      },
      user.schoolId
    )
  }

  @ApiOperation({ summary: 'get top 10 books' })
  @ApiListResult(Top10BookOfReadingTime, 200)
  @SchoolAdminAuth()
  @Get('top-10-books')
  async top10Books(@CurrentSchoolAdmin() user: any) {
    return this.homepageService.getTop10Books(user.schoolId)
  }

  @ApiOperation({ summary: 'get time distribution of reading time' })
  @ApiListResult(ReadingTimeUserCountDto, 200)
  @SchoolAdminAuth()
  @Get('time-distribution')
  async timeDistribution(
    @CurrentSchoolAdmin() user: any,
    @Query() query: QueryReadingTimeDto
  ) {
    return this.readRecordService.timeDistribution(user.schoolId, query)
  }

  @SchoolAdminAuth()
  @Get('export/top-10-books')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=top 10 books.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export top 10 books' })
  async exportTop10Books(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response
  ) {
    const data = await this.homepageService.getTop10Books(user.schoolId, {
      fields: ['id', 'isbn', 'bookId', 'name'],
      relations: ['authors', 'publisher'],
    })
    const ids = data.map((item) => item.bookId)

    const counts = await this.readRecordService.countBookReadingUsers(user.schoolId, ids)
    const file = await this.excelService.buildExcel({
      name: `top10Books.${local}`,
      data: data.map((item) => ({
        type: local === ELocaleType.ZH_HK ? '全部' : 'ALL',
        isbn: getISBN(item.book.isbn),
        bookName: item.book.name.zh_HK,
        author: item.book.authors?.map((item) => getAuthorNameZh(item.name)).join(','),
        readingUsers:
          counts.find((count) => count.bookId === item.bookId)?.totalUser ?? 0,
        readingTime: Number(item.totalReadingTime) / 3600,
        publisher: item.book.publisher?.name.zh_HK ?? '',
      })),
    })

    await this.logService.save('下载订阅版閱讀時數 TOP 10', user)
    res.send(Buffer.from(file))
  }

  @SchoolAdminAuth()
  @Get('export/students')
  @CurrentLocaleHeader()
  @ApiBaseResult(BooleanResponse, 200, 'Export student reading time report')
  @ApiOperation({ summary: 'export student reading' })
  async exportStudents(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentSchoolAdmin() user: any,
    @Query() query: ExportStudentReadingTimeDto
  ) {
    await this.taskService.deliver(
      ETaskType.DATA_EXPORT,
      {
        local,
        user,
        query,
        type: EDataExportType.EXPORT_SCHOOL_READING_TIME_FROM_SCHOOL_ADMIN,
      },
      {}
    )
    return {
      status: true,
    }
  }

  @SchoolAdminAuth()
  @Get('export/students/read-report')
  @Header('Content-Type', 'application/zip')
  @Header('Content-Disposition', 'attachment; filename=students.zip')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export student reading' })
  async exportStudentsReadReport(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentSchoolAdmin() schoolAdmin: any,
    @Query() query: ExportStudentReadingTimeDto,
    @Res() res: Response
  ) {
    let readPeriod = ''
    if (query.startTime && query.endTime) {
      readPeriod =
        moment.tz(query.startTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD') +
        ' - ' +
        moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD')
    }
    let logoResizeBuffer = null
    const version = query.version ?? EBookVersion.SUBSCRIPTION
    const students = await this.getReadInfoOfStudent(query, schoolAdmin, version)
    const school = await this.schoolService.findOne({ where: { id: schoolAdmin.schoolId  } })
    try {
      const logoImage = await fetch(SJRC_LOGO_JPG)
      const logoBuffer = Buffer.from(await logoImage.arrayBuffer())
      const logoResize = await sharp(logoBuffer).resize(300).png({ quality: 90 }) //压缩图片
      logoResizeBuffer = await logoResize.toBuffer()
    } catch (error) {
      console.error('logo error', error)
    }
    let schoolLogoResizeBuffer = null
    let schoolLogoPictureSpacing = 0
    try {
      if (school.logo) {
        const schoolLogoImage = await fetch(school.logo)
        const schoolLogoBuffer = Buffer.from(await schoolLogoImage.arrayBuffer())
        const resize = await sharp(schoolLogoBuffer).resize(300).png({ quality: 90 }) //压缩图片
        schoolLogoResizeBuffer = await resize.toBuffer()
        const { width, height } = await resize.metadata()
        const proportion = width / height //计算logo的宽高比
        schoolLogoPictureSpacing = 37 * proportion //计算 logo在表格的间距
      }
    } catch (error) {
      console.error('school logo error', error)
    }
    const zip = new AdmZip()
    for (const student of students) {
      const { books, schoolName, studentName } = student
      if (books?.length === 0) {continue}
      Object.assign(student, {
        version,
        schoolName: schoolName?.zh_HK,
        schoolLogoBuffer: schoolLogoResizeBuffer,
        logoBuffer: logoResizeBuffer,
        readPeriod,
        readTotal: books?.length,
        principalName: query.principalName,
        librarianName: query.librarianName,
        gradeClass: `${student.grade}/${student.class}`,
        schoolLogoPictureSpacing,
      })
      books.map((book) => {
        const {
          name,
          total_reading_time,
          total_reading_count,
          school_year,
          old_grade,
          old_class,
        } = book
        Object.assign(book, {
          name: name?.zh_HK,
          time: Number(total_reading_time) ?? 0,
          count: Number(total_reading_count) ?? 0,
          school_year,
          old_grade_class: `${old_grade}/${old_class}`,
        })
      })
      let wb
      if (query.byYear) {
        wb = getStudentReadByYearPortExcel(student)
      } else {
        wb = getStudentReadPortExcel(student)
      }
      const studentBuffer = await wb.writeToBuffer()
      zip.addFile(`${studentName}.xlsx`, studentBuffer)
    }

    await this.logService.save('下载閱讀詳情（用戶）阅读报表', schoolAdmin, query)
    res.send(zip.toBuffer())
  }

  @SchoolAdminAuth()
  @Get('export/books')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=books.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export books reading' })
  async exportReadingBooks(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentSchoolAdmin() user: any,
    @Query() query: QueryReadingTimeDto,
    @Res() res: Response
  ) {
    const data = await this.schoolDashboardService.listStudentReadingByBook(
      query,
      user.schoolId
    )

    const file = await this.excelService.buildExcel({
      name: `readingOfBooks.${local}`,
      data: data.items.map((item: any) => ({
        startTime: moment
          .tz(query.startTime * 1000, 'Asia/Hong_Kong')
          .format('YYYY-MM-DD'),
        endTime: moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
        isbn: getISBN(item.isbn) ?? '',
        bookName: item.name?.zh_HK,
        author: item.authorName?.map((item: any) => getAuthorNameZh(item)).join(',') ?? '',
        publisher: item.publisher?.zh_HK,
        totalUser: item.totalUser ?? 0,
        totalReadingTime: Number(item.totalReadingTime) / 3600 ?? 0,
        totalReadingTimes: item.totalReadingTime ?? 0,
      })),
    })
    await this.logService.save('下载订阅版閱讀詳情（書籍）', user, query)
    res.send(Buffer.from(file))
  }

  @SchoolAdminAuth()
  @Get('export/books/test')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=books.xlsx')
  // @Header('Content-Type', 'application/zip')
  // @Header('Content-Disposition', 'attachment; filename=books.zip')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export books reading' })
  async exportReadingBooksTest(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentSchoolAdmin() user: any,
    @Query() query: QueryReadingTimeDto,
    @Res() res: Response
  ) {
    const schools = await this.schoolRepository.find({
      select: ['id', 'name', 'type'],
    })

    const dataset = []
    for (const school of schools) {
      const data = await this.schoolDashboardService.listStudentReadingByBook(
        query,
        school.id 
      )
      console.log(school.id)
      dataset.push(
        ...data.items.map((item: any) => ({
          schoolType: convertSchoolTypeToName(school.type),
          schoolName: school.name.zh_HK,
          startTime: moment
            .tz(query.startTime * 1000, 'Asia/Hong_Kong')
            .format('YYYY-MM-DD'),
          endTime: moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
          isbn: getISBN(item.isbn) ?? '',
          bookName: item.name?.zh_HK,
          author:
            item.authorName?.map((author: any) => getAuthorNameZh(author)).join(',') ?? '',
          publisher: item.publisher?.zh_HK,
          totalUser: item.totalUser ?? 0,
          totalReadingTime: Number(item.totalReadingTime) / 3600 ?? 0,
          totalReadingTimes: item.totalReadingTime ?? 0,
        }))
      )
    }

    const file = await this.excelService.buildExcel({
      name: `readingOfBooksV2.${local}`,
      data: dataset,
    })
    await this.logService.save('下载订阅版閱讀詳情（書籍）', user, query)
    res.send(Buffer.from(file))
  }

  @SchoolAdminAuth()
  @Get('export/reading-time-in-class')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=reading time in class.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export reading time in class' })
  async exportReadingTimeInClass(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentSchoolAdmin() user: any,
    @Query() query: QueryReadingTimeDto,
    @Res() res: Response
  ) {
    const file = await this.schoolDashboardService.exportReadingTimeByClass(
      user,
      query,
      local
    )
    res.send(Buffer.from(file))
  }

  @SchoolAdminAuth()
  @Get('export/user-count-in-class')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=reading-user-count-in-class.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export reading user count in class' })
  async exportReadingUserCountInClass(
    @CurrentLocale() locale: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @CurrentSchoolAdmin() user: any,
    @Query() query: QueryReadingTimeDto
  ) {
    const file = await this.schoolDashboardService.exportReadingUserCountByClass(
      user,
      query,
      locale
    )

    res.send(Buffer.from(file))
  }

  @SchoolAdminAuth()
  @Get('export/user-count-in-day')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header(
    'Content-Disposition',
    'attachment; filename=reading time and count by date.xlsx'
  )
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export reading time and user count by date' })
  async exportReadingUserCountByDay(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @CurrentSchoolAdmin() user: any,
    @Query() query: QueryReadingTimeDto
  ) {
    const data = await this.schoolDashboardService.readingUserCountAndTimeByDay(
      user,
      query
    )

    const file = await this.excelService.buildExcel({
      name: `readingTimeAndCountByDate.${local}`,
      data: data.map((item) => ({
        date: item.date,
        totalUser: item.count,
        totalReadingTime: Number(item.totalReadingTime) / 3600,
      })),
    })
    res.send(Buffer.from(file))
  }

  private async getReadInfoOfStudent(
    query: QueryStudentReadingTimeDto,
    schoolAdmin: any,
    version: EBookVersion
  ) {
    const readingService =
      version === EBookVersion.SUBSCRIPTION
        ? this.readRecordService
        : this.referenceReadService
    let userIds = []
    if (query.keyword) {
      const accounts = await this.userRepository.getUsers(
        schoolAdmin.schoolId,
        query.keyword
      )
      userIds = accounts.map((item) => item.id)
      if (userIds.length === 0) {
        return {
          items: [],
          total: 0,
          pageIndex: query.pageIndex,
          pageSize: query.pageSize,
        }
      }
    } else {
      userIds = await readingService.getReadingUserIdsBySchool(
        schoolAdmin.schoolId,
        query
      )
    }
    const students = await this.userRepository.getUsersSchoolGradeClass(
      schoolAdmin.schoolId,
      query,
      userIds
    )
    for (const student of students) {
      const books = await readingService.listReadBooks(
        student.id,
        schoolAdmin.schoolId,
        query
      )
      Object.assign(student, { books })
    }
    return students
  }
}
