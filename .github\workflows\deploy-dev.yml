name: Deploy to develop
on:
  push:
    branches:
      - develop
  workflow_dispatch:  # 👈 "Run workflow" 按钮
jobs:
  build_and_push_docker_images:
    name: My Job
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout repo
        uses: actions/checkout@v3

      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20.15.1"

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::404904371652:role/github-action-role
          aws-region: ap-southeast-1

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Pre-build validation
        run: |
          yarn install --frozen-lockfile
          yarn build

      - name: Build and tag docker image
        run: |
          IMAGE_TAG="sjrc-service-develop:latest"
          ECR_TAG="404904371652.dkr.ecr.ap-southeast-1.amazonaws.com/sjrc-service-develop:latest"

          docker build -t $IMAGE_TAG .
          docker tag $IMAGE_TAG $ECR_TAG

          # 验证镜像构建成功
          docker inspect $IMAGE_TAG

      - name: Push docker images to private ECR
        # env:
        #   REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        #   REPOSITORY: my-ecr-repo
        #   IMAGE_TAG: ${{ github.sha }}
        run: |
          docker push 404904371652.dkr.ecr.ap-southeast-1.amazonaws.com/sjrc-service-develop:latest

  deploy:
    name: deploy
    needs: build_and_push_docker_images
    runs-on: ubuntu-latest
    steps:
      - name: deploy
        env:
          PRIVATE_KEY: ${{ secrets.PRIVATE_KEY }}
        run: |
          echo "$PRIVATE_KEY" >> $HOME/key.pem
          chmod 400 $HOME/key.pem
          ssh -i $HOME/key.pem -o StrictHostKeyChecking=no ubuntu@************** "cd /home/<USER>/develop/sjrc-service && sh deploy.sh"
          echo "deploy done"
