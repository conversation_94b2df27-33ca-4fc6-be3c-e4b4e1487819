{
  "compilerOptions": {
    "module": "commonjs",
    "esModuleInterop": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "target": "es2019",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "declaration": true,
    "incremental": true,
    "strictNullChecks": false,
    "typeRoots": ["./node_modules/@types", "./src/types"],
    "paths": {
      "@test/*": ["test/*"],
      "@/common": ["src/common"],
      "@/*": ["src/*"],
      "cache-manager-redis-store": ["src/types/cache-manager-redis-store"]
    },
    "noImplictAny": false,
  },
  "exclude": ["node_modules", "dist"]
}
