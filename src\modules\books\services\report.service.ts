import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment-timezone'
import R from 'ramda'
import { In, Repository } from 'typeorm'
import { EBookVersion, EUserType } from '@/enums'
import { IAdminRepo, ISchoolAdminRepo, IUserRepo } from '@/modules/shared/interfaces'
import { ISchoolService } from '@/modules/shared/interfaces'
import { getDays } from '@/utils'
import { UserAnswer, UserAnswerCount } from '../../../entities'
import { QuerySomeCountDto } from '../dto'
import { BookRepository } from './book.repository'
import { PublisherService } from './publisher.service'
import { ViewBookDetailService } from './viewBookDetail.service'

@Injectable()
export class ReportService {
  constructor(
    @InjectRepository(UserAnswer)
    private readonly userAnswerRepositories: Repository<UserAnswer>,
    @InjectRepository(UserAnswerCount)
    private readonly userAnswerCountRepositories: Repository<UserAnswerCount>,
    private readonly userRepository: IUserRepo,
    private readonly publisherService: PublisherService,
    private readonly bookRepository: BookRepository,
    private readonly schoolService: ISchoolService,
    private readonly adminRepo: IAdminRepo,
    private readonly schoolAdminRepo: ISchoolAdminRepo,
    private readonly viewBookDetailService: ViewBookDetailService
  ) {}

  async getUVPV() {
    const result = await this.userAnswerCountRepositories.query(`
         select
            count(distinct(user_answer_count.user_id)) as totalViewUsers,
            count(*) as totalViewCount
          from
            user_answer_count
          where user_type = '${EUserType.STUDENT}'
    `)
    return {
      totalViewCount: Number(result[0]?.totalViewCount || 0),
      totalViewUsers: Number(result[0]?.totalViewUsers || 0),
    }
  }

  async getTotalSubject() {
    const result = await this.userAnswerRepositories.query(`
         select
            count(*) as totalSubjectCount
          from
            subjects
          where deleted_at is null
    `)
    return {
      totalSubjectCount: Number(result[0]?.totalSubjectCount || 0),
    }
  }

  async count(query: QuerySomeCountDto) {
    const { version = EBookVersion.SUBSCRIPTION, hasScienceRoom } = query || {}
    const filter = R.isNil(hasScienceRoom) ? { version } : { hasScienceRoom }
    const schoolIds = await this.schoolService.findSchoolIds(filter)
    const hasSchools = schoolIds.length > 0
    const [
      studentCount,
      teacherCount,
      publisherCount,
      bookCount,
      schoolCount,
      platformAdminCount,
      adminCount,
    ] = await Promise.all([
      hasSchools ? this.userRepository.count({ type: EUserType.STUDENT, schoolIds }) : 0,
      hasSchools ? this.userRepository.count({ type: EUserType.TEACHER, schoolIds }) : 0,
      this.publisherService.count(filter),
      this.bookRepository.countBooks({}, filter),
      this.schoolService.count(filter),
      this.adminRepo.count(),
      hasSchools
        ? this.schoolAdminRepo.count({ where: { school: { id: In(schoolIds) } } })
        : 0,
    ])

    let data =
      query?.version === EBookVersion.REFERENCE
        ? await this.viewBookDetailService.count()
        : {}
    if (query.hasScienceRoom) {
      data = {
        ...(await this.getUVPV()),
        ...(await this.getTotalSubject()),
      }
    }

    return {
      studentCount,
      teacherCount,
      publisherCount,
      bookCount,
      schoolCount,
      platformAdminCount,
      adminCount,
      ...data,
    }
  }

  async schoolUserCount(
    startTime: number,
    endTime: number,
    options: {
      version?: EBookVersion
      hasScienceRoom?: boolean
    }
  ) {
    const { version = EBookVersion.SUBSCRIPTION } = options
    const studentData = await this.userRepository.countByDay({
      startTime,
      endTime,
      type: EUserType.STUDENT,
      version,
      hasScienceRoom: options.hasScienceRoom,
    })

    const teacherData = await this.userRepository.countByDay({
      startTime,
      endTime,
      type: EUserType.TEACHER,
      version,
      hasScienceRoom: options.hasScienceRoom,
    })

    console.log({ studentData, teacherData })

    return getDays(startTime, endTime).map((date) => ({
      date,
      studentCount:
        studentData.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
        )?.total ?? 0,
      teacherCount:
        teacherData.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
        )?.total ?? 0,
    }))
  }

  async adminCount(startTime: number, endTime: number) {
    const adminData = await this.adminRepo.countByDay({ startTime, endTime })
    const schoolAdminData = await this.schoolAdminRepo.countByDay({
      startTime,
      endTime,
    })

    return getDays(startTime, endTime).map((date) => ({
      date,
      platformCount:
        adminData.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
        )?.total ?? 0,
      schoolCount:
        schoolAdminData.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
        )?.total ?? 0,
    }))
  }
}
