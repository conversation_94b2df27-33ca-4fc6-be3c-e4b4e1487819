import {Book} from '@/entities'
import {QueryAdminReferenceBooksDto} from '@/modules/books/dto'
import {ICountBooks, IKeywordSearchBook} from '@/modules/books/interfaces'
import {FindOneOptions} from 'typeorm'
import {EBookVersion} from '@/enums'

interface IBook {}

export  abstract class IBookRepo implements IBook{
    abstract searchBooks(query: IKeywordSearchBook,filter?: {
      authorIds?: number[]
      publisherIds?: number[]
      withDeleted?: boolean
    },options?: { fields?: string[]; relations?: string[] }): Promise<any>
    abstract listBooks(filter: any, options?: any): Promise<any>
    abstract searchBookByName(keyword: string): Promise<any>
    abstract removeBookRelationsForSchool(schoolId: number, bookIds: number[], userType: any): Promise<any>
    abstract listReferenceBooks(
      schoolId: number,
      query?: QueryAdminReferenceBooksDto,
      options?: { relations?: string[]; fields?: string[] }
    ): Promise<any>
    abstract getBook(
      filter: { id?: number; bookId?: string },
      options: { withDeleted?: boolean; relations?: string[] }
    ): Promise<Book>

    abstract getLevelHotBookIds(
        level: number[] | undefined,
        allIds: number[],
        skip: number,
        pageSize: number,
        schoolId: number,
        isTeacher: boolean
    )

    abstract findOne(options?: FindOneOptions<Book>)

    abstract findBooks(options: {
        ids?: number[]
        authorIds?: number[]
        publisherId?: number[]
        categoryIds?: number[]
        labelId?: number
        isbn?: string[]
        level?: number
        withDeleted?: boolean
    })

abstract countBooks(
    query: ICountBooks,
    options?: { version?: EBookVersion; hasScienceRoom?: boolean }
)
}