import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import Decimal from 'decimal.js'
import moment from 'moment'  
import { Between, In, Repository } from 'typeorm'
import { RedisService } from '@/common'
import { AnswerItem, Question, UserAnswer, UserAnswerCount } from '@/entities'
import { QuestionHistory } from '@/entities/questionHistory'
import { EAbility, EBadge, EQuestionType, EUserType } from '@/enums'
import { QueryReadingTimeDto } from '@/modules/books/dto'
import { PAGE_SIZE } from '@/modules/constants'
import {
  AnswerCountDto,
  AnswerQuestionDto,
  ExportDetailBySubjectDto,
  ExportGroupBySchoolDto,
  QueryAdminAnswerDto,
  QueryAnswersDto,
  QueryAnswerStatisticsDto,
  QuerySchoolUserAnswerDto,
  QueryTimeDto,
  TimesAndUsersGroupByDateDto,
} from '../dto'
import { QuestionNotExistException, SubjectNotExistException } from '../exception'
import { SubjectService } from './subject.service'
import { IUserRepo } from '@/modules/shared/interfaces'

@Injectable()
export class UserAnswerService {
  constructor(
    @InjectRepository(UserAnswer)
    private readonly userAnswerRepositories: Repository<UserAnswer>,
    @InjectRepository(UserAnswerCount)
    private readonly userAnswerCountRepositories: Repository<UserAnswerCount>,
    @InjectRepository(Question)
    private readonly questionRepositories: Repository<Question>,
    @InjectRepository(QuestionHistory)
    private questionHistoryRepository: Repository<QuestionHistory>,
    private readonly subjectService: SubjectService,
    private readonly redisService: RedisService,
    private readonly userRepositories: IUserRepo
  ) {
  }

  async latestAnswer(subjectId: number, userId: number) {
    const [row] = await this.userAnswerRepositories.query(`
        select time, correct_count, question_count, badge from user_answer where user_id = ${userId} and subject_id = ${subjectId} order by id desc limit 1
      `)
    return {
      badge: row?.badge || [],
      time: row?.time || 0,
      correctCount: row?.correct_count || 0,
      questionCount: row?.question_count || 0,
      // scores: row?.correct_count
      //   ? Math.ceil(((row.correct_count / row.question_count) * 10000) / 100)
      //   : 0,
    }
  }

  async answerQuestion(
    data: AnswerQuestionDto,
    userId: number,
    schoolId: number,
    classId: number,
    gradeId: number
  ) {
    const subject = await this.subjectService.getSubjectFromCache(data.subjectId)
    if (!subject) {
      return new SubjectNotExistException()
    }

    subject.questions = subject.questions.filter(this.filterQA)
    const question = subject.questions.find((item) => item.id === data.questionId)
    if (!question) {
      return new QuestionNotExistException()
    }

    // 答题中途退出，redis cache的处理相关问题：
    // 1. 重新进入答题，假如问题资料没修改，则会修改上一次的答案和时间
    //    这将导致，出现连续答题徽章问题。
    // 2. 所以退出要清除之前答题cache。

    let answerData = await this.redisService.getByJson(
      this.getAnswerCacheKey(userId, data.subjectId)
    )

    if (!answerData || data.questionIdx === 0) {
      // 退出要清除之前答题cache 或者 重置cache
      answerData = {
        answers: [],
        badge: [],
        startTime: new Date().getTime(),
      }
    }

    const answer = answerData.answers.find((item) => item.questionId === data.questionId)
    if (answer) {
      answer.selectedOptionIds = data.selectedOptionIds
      answer.usedTime = data.usedTime
    } else {
      answerData.answers.push(data)
    }

    const time = answerData.answers.reduce(
      (acc, cur) => acc + Number(cur.usedTime || 0),
      0
    )
    const { badge, comingBadge } = this.getBadge(
      answerData.answers,
      subject.questions,
      subject.countdown,
      time
    )
    // let [preMaxBadgeRow] = await this.userAnswerRepositories.query(`
    //   select id, badge from user_answer where user_id = ${userId} and subject_id = ${data.subjectId} order by max_badge desc limit 1
    // `)
    // const preMaxBadge = preMaxBadgeRow?.badge || []
    const newBadge = badge.filter(
      // 本次获得过成就，答题过程不再展示。
      (v) => !answerData.badge.includes(v)
      // 以前获得过成就
      // && !preMaxBadge.includes(v),
    )
    const allBadge = Array.from(new Set([...answerData.badge, ...badge]))

    const result = {
      questionId: question.id,
      correctOptions: question.options.filter((item) => item.isCorrect),
      isCorrect: question.options.every((option) =>
        option.isCorrect
          ? data.selectedOptionIds.includes(option.id)
          : !data.selectedOptionIds.includes(option.id)
      ),
      newBadge,
      comingBadge,
    }
    if (answerData.answers.length === subject.questions.length) {
      const lastHistory = await this.questionHistoryRepository.findOne({
        where: {
          subjectId: data.subjectId,
        },
        order: {
          id: 'DESC',
        },
      })
      let saveRow
      if (!lastHistory) {
        saveRow = await this.questionHistoryRepository.save({
          subjectId: data.subjectId,
          questions: subject.questions,
        })
      }
      // const time = (new Date().getTime() - answerData.startTime) / 1000
      const correctCount = this.getCorrectCount(answerData.answers, subject.questions)
      await this.userAnswerRepositories.save({
        time,
        answers: answerData.answers,
        correctCount: correctCount,
        questionCount: subject.questions.length,
        // scores: this.getScores(answerData.answers, subject.questions),
        questionsHistory: {
          id: (lastHistory || saveRow)?.id,
        },
        scores: Math.round((correctCount / subject.questions.length) * 10000) / 100,
        badge: allBadge,
        maxBadge: allBadge.length ? allBadge.length : 0,
        user: { id: userId },
        school: { id: schoolId },
        subject: { id: data.subjectId },
        userClass: classId ? { id: classId } : null,
        gradeId: gradeId ? gradeId : null,
      })
      return {
        ...result,
        badge: allBadge,
      }
    }
    await this.redisService.set(
      this.getAnswerCacheKey(userId, data.subjectId),
      JSON.stringify({
        ...answerData,
        badge: allBadge,
      }),
      60 * 60 * 24
    )

    return result
  }

  async answerCount(
    data: AnswerCountDto,
    userId: number,
    schoolId: number,
    classId: number,
    gradeId: number,
    isTeacher: boolean
  ) {
    //限制15秒内只能提交1次
    const userAnswerStart = await this.redisService.get(
      this.getAnswerCountCacheKey(userId, data.subjectId)
    )
    if (!userAnswerStart) {
      await this.redisService.set(
        this.getAnswerCountCacheKey(userId, data.subjectId),
        'getAnswerCountCacheKey',
        15
      )

      await this.userAnswerCountRepositories.save({
        user: { id: userId },
        school: { id: schoolId },
        subject: { id: data.subjectId },
        userClass: classId ? { id: classId } : null,
        gradeId: gradeId ? gradeId : null,
        userType: isTeacher ? EUserType.TEACHER : EUserType.STUDENT,
      })
    }
    return {}
  }

  async timesGroupBySubject(query: QueryAdminAnswerDto) {
    console.log(query)
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query

    const [count] = await this.userAnswerCountRepositories.query(`
      select
        count(*) as total
      from
        (
          select
            count(*) as times,
            subject_id as subjectId
          from
            user_answer_count
          where
            unix_timestamp(user_answer_count.created_at) > ${query.startTime}
            and unix_timestamp(user_answer_count.created_at) < ${query.endTime}
            and user_type = '${EUserType.STUDENT}'
          group by
            subject_id
        ) as t
    `)

    if (Number(count.total) === 0) {
      return { total: 0, items: [], pageIndex, pageSize }
    }

    const items = await this.userAnswerCountRepositories.query(`
      select
        times,
        subjects.id,
        subjects.name
      from
        subjects
        inner join (
          select
            count(*) as times,
            subject_id as subjectId
          from
            user_answer_count
          where
            unix_timestamp(user_answer_count.created_at) > ${query.startTime}
            and unix_timestamp(user_answer_count.created_at) < ${query.endTime}
            and user_type = '${EUserType.STUDENT}'
          group by
            subject_id
        ) as t on t.subjectId = subjects.id
      order by
        times desc
      limit ${pageSize} offset ${(pageIndex - 1) * pageSize}
    `)

    return { total: Number(count.total), items, pageIndex, pageSize }
  }

  async getAllQAHistory(query: QueryAdminAnswerDto, schoolId: number) {
    const list = await this.userAnswerRepositories.find({
      where: {
        school: { id: schoolId },
        createdAt: Between(
          moment(query.startTime * 1000).toDate(),
          moment(query.endTime * 1000).toDate()
        ),
      },
      select: ['id', 'scores', 'answers', 'user', 'subject', 'questionsHistory'],
      relations: ['questionsHistory', 'user', 'subject'],
    })
    return list
  }

  async usersGroupBySubject(query: QueryAdminAnswerDto) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query

    const [count] = await this.userAnswerCountRepositories.query(`
      select
        count(*) as total
      from
        (
          select
            count(distinct(user_id)) as users,
            subject_id as subjectId
          from
            user_answer_count
          where
            unix_timestamp(user_answer_count.created_at) > ${query.startTime}
            and unix_timestamp(user_answer_count.created_at) < ${query.endTime}
            and user_type = '${EUserType.STUDENT}'
          group by
            subject_id
        ) as t
    `)

    if (Number(count.total) === 0) {
      return { total: 0, items: [], pageIndex, pageSize }
    }

    const items = await this.userAnswerCountRepositories.query(`
      select
        users,
        subjects.id,
        subjects.name
      from
        subjects
        inner join (
          select
            count(distinct(user_id)) as users,
            subject_id as subjectId
          from
            user_answer_count
          where
            unix_timestamp(user_answer_count.created_at) > ${query.startTime}
            and unix_timestamp(user_answer_count.created_at) < ${query.endTime}
            and user_type = '${EUserType.STUDENT}'
          group by
            subject_id
        ) as t on t.subjectId = subjects.id
      order by
        users desc
      limit ${pageSize} offset ${(pageIndex - 1) * pageSize}
    `)

    return { total: Number(count.total), items, pageIndex, pageSize }
  }

  async usersGroupBySchool(query: QueryAdminAnswerDto) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query

    const [count] = await this.userAnswerCountRepositories.query(`
      select
        count(*) as total
      from
        (
          select
            count(distinct(user_id)) as users,
            school_id as schoolId
          from
            user_answer_count
          where
            unix_timestamp(user_answer_count.created_at) > ${query.startTime}
            and unix_timestamp(user_answer_count.created_at) < ${query.endTime}
            and user_type = '${EUserType.STUDENT}'
          group by
            school_id
        ) as t
    `)

    if (Number(count.total) === 0) {
      return { total: 0, items: [], pageIndex, pageSize }
    }

    const items = await this.userAnswerCountRepositories.query(`
      select
        users,
        schools.id,
        schools.name
      from
        schools
        inner join (
          select
            count(distinct(user_id)) as users,
            school_id as schoolId
          from
            user_answer_count
          where
            unix_timestamp(user_answer_count.created_at) > ${query.startTime}
            and unix_timestamp(user_answer_count.created_at) < ${query.endTime}
            and user_type = '${EUserType.STUDENT}'
          group by
            school_id
        ) as t on t.schoolId = schools.id
      order by
        users desc
      limit ${pageSize} offset ${(pageIndex - 1) * pageSize}
    `)

    return { total: Number(count.total), items, pageIndex, pageSize }
  }

  async timesGroupBySchool(query: QueryAdminAnswerDto) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query

    const [count] = await this.userAnswerCountRepositories.query(`
      select
        count(*) as total
      from
        (
          select
            count(*) as users,
            school_id as schoolId
          from
            user_answer_count
          where
            unix_timestamp(user_answer_count.created_at) > ${query.startTime}
            and unix_timestamp(user_answer_count.created_at) < ${query.endTime}
            and user_type = '${EUserType.STUDENT}'
          group by
            school_id
        ) as t
    `)

    if (Number(count.total) === 0) {
      return { total: 0, items: [], pageIndex, pageSize }
    }

    const items = await this.userAnswerCountRepositories.query(`
      select
        users,
        schools.id,
        schools.name
      from
        schools
        inner join (
          select
            count(*) as users,
            school_id as schoolId
          from
            user_answer_count
          where
            unix_timestamp(user_answer_count.created_at) > ${query.startTime}
            and unix_timestamp(user_answer_count.created_at) < ${query.endTime}
            and user_type = '${EUserType.STUDENT}'
          group by
            school_id
        ) as t on t.schoolId = schools.id
      order by
        users desc
      limit ${pageSize} offset ${(pageIndex - 1) * pageSize}
    `)

    return { total: Number(count.total), items, pageIndex, pageSize }
  }

  async uvpvGroupBySchool(query: ExportGroupBySchoolDto) {
    const items = await this.userAnswerCountRepositories.query(`
      select
        times,
        users,
        schools.id,
        schools.name as schoolName,
        schools.region as region
      from
        schools
        inner join (
          select
            count(*) AS times, 
            count( DISTINCT ( user_id )) AS users,
            school_id as schoolId
          from
            user_answer_count
          where
            unix_timestamp(user_answer_count.created_at) > ${query.startTime}
            and unix_timestamp(user_answer_count.created_at) < ${query.endTime}
            and user_type = '${EUserType.STUDENT}'
          group by
            school_id
        ) as t on t.schoolId = schools.id
      order by
        ${query.sortBy == 'users' ? 'users' : 'times'} desc
    `)

    return { items }
  }

  async top10Subject(schoolId: number, query: QueryTimeDto) {
    const timeCondition =
      query.startTime && query.endTime
        ? `(created_at BETWEEN "${new Date(
          query.startTime * 1000
        ).toISOString()}" AND "${new Date(query.endTime * 1000).toISOString()}")`
        : '1=1'
    const where = `${timeCondition} AND school_id = ${schoolId} AND user_type = '${EUserType.STUDENT}'`

    // 执行查询
    return await this.userAnswerCountRepositories.query(`
      select
        times,
        subjects.id as id,
        subjects.name as name,
        subjects.image as image
      from
        (
          select
            count(*) as times,
            subject_id as subjectId
          from
            user_answer_count
          where
            ${where}
          group by
            subject_id
          order by
            times desc
          limit
            10
        ) as t
        inner join subjects on t.subjectId = subjects.id
    `)
  }

  async groupByDate(query: TimesAndUsersGroupByDateDto) {
    return this.userAnswerCountRepositories.query(`
      select
        DATE(CONVERT_TZ(created_at, 'UTC', 'Asia/Hong_Kong')) as date,
        count(*) as times,
        count(distinct user_id) as users
      from
        user_answer_count
      where
        unix_timestamp(user_answer_count.created_at) > ${query.startTime}
        and unix_timestamp(user_answer_count.created_at) < ${query.endTime}
        ${query.schoolId ? `and school_id = ${query.schoolId}` : ''}
        ${query.subjectId ? `and subject_id = ${query.subjectId}` : ''}
        and user_type = '${EUserType.STUDENT}'
      group by
        date
    `)
  }

  async queryPVByGrade(query: QueryTimeDto, schoolId: number) {
    return await this.userAnswerRepositories.query(`
      select
        grades.id as gradeId,
        user_class.id as classId,
        grades.grade as gradeName,
        user_class.class as className,
        ifnull(t.times, 0) as times
      from
        user_class
        left join grades on grades.id = user_class.grade_id
        left join(
          select
            user_class.grade_id as gradeId,
            user_class.id as classId,
            count(*) as times
          from
            user_answer_count
            inner join users on user_answer_count.user_id = users.id
            inner join user_class on users.user_class_id = user_class.id
          where
            user_answer_count.school_id = ${schoolId}
            and unix_timestamp(user_answer_count.created_at) > ${query.startTime}
            and unix_timestamp(user_answer_count.created_at) < ${query.endTime}
            and user_type = '${EUserType.STUDENT}'
          group by
            user_class.id,
            user_class.grade_id
        ) as t on user_class.id = t.classId
      where grades.school_id = ${schoolId} and user_class.deleted_at is null
      order by
        grades.sequence asc,
        user_class.sequence asc;
    `)
  }
  async timesGroupByGrade(query: QueryTimeDto, schoolId: number) {
    const data = await this.queryPVByGrade(query, schoolId)
    // const teachData = await this.queryPVByTeach(query, schoolId)
    // const teachPV = Number(teachData[0]?.times || 0)
    return [...new Set(data.map((item) => item.gradeId))].map((gradeId) => {
      const classes = data
        .filter((item) => item.gradeId === gradeId)
        .map((item) => ({
          id: item.classId,
          name: item.className,
          times: Number(item.times || 0),
        }))
      return {
        id: gradeId,
        name: data.find((item) => item.gradeId === gradeId).gradeName,
        classes,
        times: classes.reduce((acc, cur) => acc + Number(cur.times || 0), 0),
      }
    })
    // .concat({
    //   id: -999,
    //   name: '教职员',
    //   classes: [],
    //   times: teachPV,
    // })
  }

  async queryUVByGrade(query: QueryTimeDto, schoolId: number) {
    return await this.userAnswerCountRepositories.query(`
      select
        grades.id as gradeId,
        user_class.id as classId,
        grades.grade as gradeName,
        user_class.class as className,
        ifnull(t.times, 0) as times
      from
        user_class
        left join grades on grades.id = user_class.grade_id
        left join(
          select
            user_class.grade_id as gradeId,
            user_class.id as classId,
            count(distinct(user_answer_count.user_id)) as times
          from
            user_answer_count
            inner join users on user_answer_count.user_id = users.id
            inner join user_class on users.user_class_id = user_class.id
          where
            user_answer_count.school_id = ${schoolId}
            and unix_timestamp(user_answer_count.created_at) > ${query.startTime}
            and unix_timestamp(user_answer_count.created_at) < ${query.endTime}
            and user_answer_count.user_type = '${EUserType.STUDENT}'
          group by
            user_class.id,
            user_class.grade_id
        ) as t on user_class.id = t.classId
      where grades.school_id = ${schoolId} and user_class.deleted_at is null
      order by
        grades.sequence asc,
        user_class.sequence asc;
    `)
  }
  async queryUVByTeach(query: QueryReadingTimeDto, schoolId: number) {
    return await this.userAnswerRepositories.query(`
        select
            count(distinct(user_answer.user_id)) as times
          from
            user_answer
            inner join users u on user_answer.user_id = u.id
          where
            user_answer.school_id = ${schoolId}
            and unix_timestamp(user_answer.created_at) > ${query.startTime}
            and unix_timestamp(user_answer.created_at) < ${query.endTime}
						and u.type= 'teacher'
    `)
  }
  async queryPVByTeach(query: QueryReadingTimeDto, schoolId: number) {
    return await this.userAnswerRepositories.query(`
        select
            count(*) as times
          from
            user_answer
            inner join users u on user_answer.user_id = u.id
          where
            user_answer.school_id = ${schoolId}
            and unix_timestamp(user_answer.created_at) > ${query.startTime}
            and unix_timestamp(user_answer.created_at) < ${query.endTime}
						and u.type= 'teacher'
    `)
  }
  async usersGroupByGrade(query: QueryTimeDto, schoolId: number) {
    const data = await this.queryUVByGrade(query, schoolId)
    // const teachData = await this.queryUVByTeach(query, schoolId)
    // const teachUV = Number(teachData[0]?.times || 0)
    return [...new Set(data.map((item) => item.gradeId))].map((gradeId) => {
      const classes = data
        .filter((item) => item.gradeId === gradeId)
        .map((item) => ({
          id: item.classId,
          name: item.className,
          users: Number(item.times || 0),
        }))
      return {
        id: gradeId,
        name: data.find((item) => item.gradeId === gradeId).gradeName,
        classes,
        users: classes.reduce((acc, cur) => acc + Number(cur.users || 0), 0),
      }
    })
    // .concat({
    //   id: -999,
    //   name: '教职员',
    //   classes: [],
    //   users: teachUV,
    // })
  }

  async groupByUserAndSubject(
    query: QuerySchoolUserAnswerDto,
    schoolId: number,
    options: {
      gradeScores?: boolean
    } = {}
  ) {
    const {
      pageIndex,
      pageSize,
      keyword,
      orderDirection = 'desc',
    } = query as QuerySchoolUserAnswerDto
    let sortBy = query.sortBy

    if (
      ![
        'className',
        'gradeName',
        'users',
        'times',
        'maxScores',
        'minScores',
        'gradeScores',
        'classScores',
      ].includes(sortBy)
    ) {
      sortBy = undefined
    }
    let whereClause = `user_answer.school_id = ${schoolId}`
    if (keyword) {
      const users = await this.userRepositories.searchUserIds(schoolId, { keyword })
      const userCondition = users.length
        ? `user_answer.user_id in (${users.map((item) => item.id).join(',')})`
        : null

      const subjectCondition = `subjects.all_name LIKE '%${keyword}%'`

      // 组合过滤条件
      const keywordFilter = [userCondition, subjectCondition].filter(Boolean).join(' OR ')

      if (!keywordFilter) {
        return { total: 0, items: [], pageIndex, pageSize }
      }

      whereClause += ` AND (${keywordFilter})`
    }

    const groupByUserAndSubject = `
      select
        t1.userId,
        t1.subjectId,
        t1.times,
        t1.maxScores,
        t1.minScores,
        t1.avgScores,
        ${options.gradeScores ? 'grade.gradeScores,' : ''}
        c.classScores
      from (
        select
          user_answer.user_id as userId,
          user_answer.subject_id as subjectId,
		      user_answer.grade_id as grade_id,
          count(*) as times,
          max(user_answer.scores) as maxScores,
          min(user_answer.scores) as minScores,
          avg(user_answer.scores) as avgScores
        from
          user_answer
          inner join users on users.id = user_answer.user_id
          inner join user_class on user_class.id = users.user_class_id
          inner join subjects on subjects.id = user_answer.subject_id
        where
          ${whereClause}
          and unix_timestamp(user_answer.created_at) > ${query.startTime}
          and unix_timestamp(user_answer.created_at) < ${query.endTime} 
          ${query.gradeId ? `and user_class.grade_id = ${query.gradeId}` : ''} 
          ${query.classId ? `and user_class.id = ${query.classId}` : ''}
        group by
          user_answer.user_id,
		      user_answer.grade_id,
          user_answer.subject_id
      ) as t1
      inner join users on users.id = t1.userId
      left join (
        select
          user_class_id as class_id,
          AVG(scores) as classScores
        from
          user_answer
        where
          school_id = ${schoolId}
          and unix_timestamp(user_answer.created_at) > ${query.startTime}
          and unix_timestamp(user_answer.created_at) < ${query.endTime}
        group by
          user_class_id
          ) as c on users.user_class_id = c.class_id
       ${
  options.gradeScores
    ? `LEFT JOIN ( SELECT grade_id, AVG( scores ) AS gradeScores FROM user_answer WHERE school_id = ${schoolId} AND unix_timestamp( user_answer.created_at ) > ${query.startTime} 
        AND unix_timestamp( user_answer.created_at ) < ${query.endTime} 
        GROUP BY
        grade_id
        ) AS grade ON t1.grade_id = grade.grade_id`
    : ''
}
    
    `
    const [count] = await this.userAnswerRepositories.query(`
      select
        count(*) as total
      from
        (
          ${groupByUserAndSubject}
        ) as t
    `)

    if (Number(count.total) === 0) {
      return { total: 0, items: [], pageIndex, pageSize }
    }

    const items = await this.userAnswerRepositories.query(`
      select
        users.id as userId,
        users.given_name as userName,
        users.serial_no as serialNo,
        users.email as email,
        subjects.id as subjectId,
        subjects.name as subjectName,
        subjects.grade as subjectGrade,
        subject_categories.name as subjectCategoryName,
        themes.name as themeName,
        grades.id as gradeId,
        grades.grade as gradeName,
        user_class.id as classId,
        user_class.class as className,
        ifnull(t.times, 0) as times,
        ROUND(ifnull(t.maxScores, 0), 2) as maxScores,
        ROUND(ifnull(t.minScores, 0), 2) as minScores,
        ROUND(ifnull(t.avgScores, 0), 2) as avgScores,
        ${options.gradeScores ? `ROUND(ifnull(t.gradeScores, 0), 2) as gradeScores,` : ''}
        ROUND(ifnull(t.classScores, 0), 2) as classScores
      from
        (
          ${groupByUserAndSubject}
        ) as t
        inner join users on users.id = t.userId
        inner join subjects on subjects.id = t.subjectId
        inner join subject_categories on subjects.subject_category_id = subject_categories.id
        inner join themes on subjects.theme_id = themes.id
        inner join user_class on user_class.id = users.user_class_id
        inner join grades on grades.id = user_class.grade_id
      ${sortBy ? `order by ${sortBy} ${orderDirection}` : ''}
      ${pageSize ? `limit ${pageSize} offset ${(pageIndex - 1) * pageSize}` : ''}
    `)

    return { total: Number(count.total), items, pageIndex, pageSize }
  }

  async groupBySubject(
    query: QuerySchoolUserAnswerDto | ExportDetailBySubjectDto,
    schoolId?: number
  ) {
    const {
      pageIndex,
      pageSize,
      keyword,
      orderDirection = 'desc',
    } = query as QuerySchoolUserAnswerDto
    let sortBy = query.sortBy
    if (!['users', 'times', 'maxScores', 'minScores', 'gradeScores'].includes(sortBy)) {
      sortBy = undefined
    }

    let sids = []
    if (keyword) {
      const subjects = await this.subjectService.listSubjects({ schoolId, keyword })
      if (subjects.items.length === 0) {
        return { total: 0, items: [], pageIndex, pageSize }
      }
      sids = subjects.items.map((item) => item.id)
    }
    const conditionStr = [
      schoolId ? `user_answer.school_id = ${schoolId}` : '',
      sids.length ? `user_answer.subject_id in (${sids.join(',')})` : '',
      `unix_timestamp(user_answer.created_at) > ${query.startTime}`,
      `unix_timestamp(user_answer.created_at) < ${query.endTime}`,
      query.gradeId ? `user_class.grade_id = ${query.gradeId}` : '',
      query.classId ? `user_class.id = ${query.classId}` : '',
    ]
      .filter((v) => v)
      .join(' and ')

    const answerCountConditionStr = [
      schoolId ? `user_answer_count.school_id = ${schoolId}` : '',
      sids.length ? `user_answer_count.subject_id in (${sids.join(',')})` : '',
      `unix_timestamp(user_answer_count.created_at) > ${query.startTime}`,
      `unix_timestamp(user_answer_count.created_at) < ${query.endTime}`,
      query.gradeId ? `user_class.grade_id = ${query.gradeId}` : '',
      query.classId ? `user_class.id = ${query.classId}` : '',
    ]
      .filter((v) => v)
      .join(' and ')

    const conditionStr2 = [
      schoolId ? `user_answer.school_id = ${schoolId}` : '',
      `unix_timestamp(user_answer.created_at) > ${query.startTime}`,
      `unix_timestamp(user_answer.created_at) < ${query.endTime}`,
    ]
      .filter((v) => v)
      .join(' and ')
    console.log(conditionStr, conditionStr2)

    const groupByUserAndSubject = `
    select
      t.subjectId,
      t.times,
      t.userCounts,
      t.maxScores,
      t.minScores,
      t.gradeId,
      g.gradeScores,
      uac.userAnswerCountTimes,
      uac.userAnswerCountUsers
    from (
      select
        user_answer.subject_id as subjectId,
        count(*) as times,
        count(distinct(user_answer.user_id)) as userCounts,
        max(user_answer.scores) as maxScores,
        min(user_answer.scores) as minScores,
        user_class.grade_id as gradeId
      from
        user_answer 
        inner join users on users.id = user_answer.user_id
        inner join user_class on user_class.id = users.user_class_id
      where
        ${conditionStr}
      group by
        user_answer.subject_id,
        user_class.grade_id
    ) as t
    left join (
      select
        grade_id,
        ROUND(AVG(scores), 2) as gradeScores
      from
        user_answer
      where
        ${conditionStr2}
      group by
        grade_id
    ) as g on g.grade_id = t.gradeId
    LEFT JOIN (
    SELECT
      user_answer_count.subject_id AS subjectId,
      user_class.grade_id AS gradeId,
      COUNT(*) AS userAnswerCountTimes,
      COUNT(DISTINCT(user_answer_count.user_id)) AS userAnswerCountUsers
    FROM
      user_answer_count
      INNER JOIN users ON users.id = user_answer_count.user_id
      INNER JOIN user_class ON user_class.id = users.user_class_id
    WHERE
      ${answerCountConditionStr}
    GROUP BY
      user_answer_count.subject_id,
      user_class.grade_id
  ) AS uac ON uac.subjectId = t.subjectId AND uac.gradeId = t.gradeId
    `
    const [count] = await this.userAnswerRepositories.query(`
      select
        count(*) as total
      from
        (
          ${groupByUserAndSubject}
        ) as t
    `)

    if (Number(count.total) === 0) {
      return { total: 0, items: [], pageIndex, pageSize }
    }

    const items = await this.userAnswerRepositories.query(`
      select
        subjects.id as subjectId,
        subjects.name as subjectName,
        grades.grade as grade,
        subjects.grade as subjectGrade,
        subject_categories.name as subjectCategoryName,
        themes.name as themeName,
        t.gradeScores as gradeScores,
        ifnull(t.userAnswerCountUsers, 0) as users,
        ifnull(t.userAnswerCountTimes, 0) as times,
        ifnull(t.minScores, 0) as minScores,
        ifnull(t.maxScores, 0) as maxScores
      from
        (
          ${groupByUserAndSubject}
        ) as t
      inner join subjects on subjects.id = t.subjectId
      inner join grades on grades.id = t.gradeId
      inner join subject_categories on subjects.subject_category_id = subject_categories.id
      inner join themes on subjects.theme_id = themes.id
      ${sortBy ? `order by ${sortBy} ${orderDirection}` : ''}
      ${pageSize ? `limit ${pageSize} offset ${(pageIndex - 1) * pageSize}` : ''}
    `)
    // console.log(`${sortBy ? `order by ${sortBy} ${orderDirection}` : ''}`)

    return { total: Number(count.total), items, pageIndex, pageSize }
  }

  async answers(query: QueryAnswersDto, schoolId: number) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const [items, total] = await this.userAnswerRepositories.findAndCount({
      where: {
        user: { id: query.userId },
        subject: { id: query.subjectId },
        school: { id: schoolId },
        createdAt: Between(
          moment(query.startTime * 1000).toDate(),
          moment(query.endTime * 1000).toDate()
        ),
      },
      order: ['createdAt', 'scores'].includes(query.sortBy)
        ? { [query.sortBy]: query.orderDirection || 'DESC' }
        : {},
      take: pageSize,
      skip: (pageIndex - 1) * pageSize,
      select: ['id', 'scores', 'answers', 'createdAt', 'questionsHistory'],
      relations: ['questionsHistory'],
    })
    // 应该用问题快照
    const questionHistories = await this.questionHistoryRepository.find({
      where: { subjectId: query.subjectId },
    })
    const curQuestions = await this.questionRepositories.find({
      where: { subject: { id: query.subjectId } },
      order: { sequence: 'ASC' },
      select: ['id', 'sequence', 'options', 'name', 'description'],
    })

    return {
      pageIndex,
      pageSize,
      total,
      items: items.map((item) => {
        const questionsHistory = questionHistories.find(
          (v) => v.id == item.questionsHistory?.id
        )
        let questions = questionsHistory?.questions
        if (!questions) {
          questions = curQuestions
        }
        const data = questions.filter(this.filterQA).map((question) => {
          const answer = item.answers.find((a) => question.id === a.questionId)
          return {
            questionId: question.id,
            sequence: question.sequence,
            name: question.name,
            description: question.description,
            correctOptions: question.options.filter((item) => item.isCorrect),
            selectedOptions: question.options.filter((item) =>
              answer?.selectedOptionIds?.includes(item.id)
            ),
          }
        })
        return {
          id: item.id,
          scores: item.scores,
          answers: data,
          createdAt: item.createdAt,
        }
      }),
    }
  }

  async answerStatisticsByUserAndSubject(query: QueryAnswerStatisticsDto) {
    const [data] = await this.userAnswerRepositories.query(`
      select
        count(*) as times,
        max(scores) as maxScores,
        min(scores) as minScores,
        avg(scores) as avgScores
      from
        user_answer
      where
        user_id = ${query.userId}
        and school_id = ${query.schoolId}
        and subject_id = ${query.subjectId}
        and unix_timestamp(user_answer.created_at) > ${query.startTime}
        and unix_timestamp(user_answer.created_at) < ${query.endTime} 
    `)

    const [userLastAnswer] = await this.userAnswerRepositories.query(
      `
      select
        user_class_id, grade_id
      from
        user_answer
      where user_id=${query.userId} 
      and school_id = ${query.schoolId}
      and subject_id=${query.subjectId}
      limit 1
      `
    )

    const [avgScoresByClass] = await this.userAnswerRepositories.query(`
      select
        AVG(scores) as avgScores
      from
        user_answer
        inner join users on users.id = user_answer.user_id
        inner join user_class on user_class.id = users.user_class_id
      where
        user_answer.user_class_id = ${userLastAnswer?.user_class_id}
        and user_answer.school_id = ${query.schoolId}
        and user_answer.subject_id = ${query.subjectId}
        and unix_timestamp(user_answer.created_at) > ${query.startTime}
        and unix_timestamp(user_answer.created_at) < ${query.endTime} 
    `)

    const [avgScoresByGrade] = await this.userAnswerRepositories.query(`
      select
        AVG(scores) as avgScores
      from
        user_answer
        inner join users on users.id = user_answer.user_id
        inner join user_class on user_class.id = users.user_class_id
      where
        user_answer.grade_id = ${userLastAnswer?.grade_id}
        and user_answer.school_id = ${query.schoolId}
        and user_answer.subject_id = ${query.subjectId}
        and unix_timestamp(user_answer.created_at) > ${query.startTime}
        and unix_timestamp(user_answer.created_at) < ${query.endTime} 
    `)

    const [user] = await this.userAnswerRepositories.query(`
      select
        users.id,
        users.given_name as userName,
        user_class.class as className,
        grades.grade as gradeName
      from
        users
        inner join user_class on users.user_class_id = user_class.id
        inner join grades on user_class.grade_id = grades.id
      where
        users.id = ${query.userId}
    `)

    const { ability, subjectName } = await this.getMaxScoresAndAbility(query)
    return {
      ...user,
      subjectName,
      ability,
      times: Number(data.times || 0),
      maxScores: Number(data.maxScores || 0),
      minScores: Number(data.minScores || 0),
      avgScores: Number(data.avgScores || 0),
      avgScoresByClass: Number(avgScoresByClass?.avgScores || 0),
      avgScoresByGrade: Number(avgScoresByGrade?.avgScores || 0),
    }
  }

  async maxAnswer(subjectId: number, userId: number) {
    const [badgeRow] = await this.userAnswerRepositories.query(`
      select id, badge from user_answer where user_id = ${userId} and subject_id = ${subjectId} order by max_badge desc limit 1
    `)
    if (!badgeRow) {
      return undefined
    }

    const data = await this.getMaxScoresAndAbility({ userId, subjectId } as any)

    return {
      // totalScores: data.totalScores,
      totalTime: data.totalTime,
      // scores: data.maxScores,
      correctCount: data.correctCount,
      questionCount: data.questionCount,
      time: data.time,
      ability: data.ability,
      badge: badgeRow?.badge || [],
    }
  }

  async getAnswerResultBySubjectIds(subjectIds: number[], userId: number) {
    return await this.userAnswerRepositories.find({
      where: {
        user: {
          id: userId,
        },
        subject: { id: In(subjectIds) },
      },
      relations: ['user', 'subject'],
      select: ['id'],
    })
  }

  private async getMaxScoresAndAbility(query: QueryAnswerStatisticsDto) {
    // questions_history_id 每次修改能力值就会变动 需求以最新的能力值变动为主，然后从最新的能力值取最高分数的那个数据
    let [ability] = await this.userAnswerRepositories.query(`
    select id, answers, questions_history_id, correct_count, question_count, time, scores from user_answer where user_id = ${
  query.userId
} and subject_id = ${query.subjectId} ${
  query.startTime && query.endTime
    ? `and unix_timestamp(user_answer.created_at) > ${query.startTime}
      and unix_timestamp(user_answer.created_at) < ${query.endTime} `
    : ''
}order by questions_history_id desc,scores desc limit 1
  `)
    ability ??= {}

    const subject = await this.subjectService.getSubject(query.subjectId)
    let hisAbilities = []
    let hisQuestions = []
    if (ability?.questions_history_id) {
      const his = await this.questionHistoryRepository.findOne({ where: { id: ability.questions_history_id,
      } })
      hisQuestions = his?.questions || subject.questions

      hisAbilities = this.getAbilities(hisQuestions)
    }
    subject.questions = subject.questions.filter(this.filterQA)
    const comprehensionScore = this.getQuestionAbilityScore(
      hisQuestions,
      EAbility.COMPREHENSION
    )
    console.log(comprehensionScore)
    const comprehensionAnswerScore = this.getAnswerAbilityScore(
      ability,
      hisQuestions,
      EAbility.COMPREHENSION
    )

    console.log(comprehensionAnswerScore)

    const expressionScore = this.getQuestionAbilityScore(
      hisQuestions,
      EAbility.EXPRESSION_ABILITY
    )
    const expressionAnswerScore = this.getAnswerAbilityScore(
      ability,
      hisQuestions,
      EAbility.EXPRESSION_ABILITY
    )

    const inquiryScore = this.getQuestionAbilityScore(
      hisQuestions,
      EAbility.INQUIRY_ABILITY
    )
    const inquiryAnswerScore = this.getAnswerAbilityScore(
      ability,
      hisQuestions,
      EAbility.INQUIRY_ABILITY
    )

    const judgmentScore = this.getQuestionAbilityScore(
      hisQuestions,
      EAbility.JUDGMENT_ABILITY
    )
    const judgmentAnswerScore = this.getAnswerAbilityScore(
      ability,
      hisQuestions,
      EAbility.JUDGMENT_ABILITY
    )

    const analyzeScore = this.getQuestionAbilityScore(
      hisQuestions,
      EAbility.SKILLS_OF_ANALYZE
    )

    const analyzeAnswerScore = this.getAnswerAbilityScore(
      ability,
      hisQuestions,
      EAbility.SKILLS_OF_ANALYZE
    )

    const solveScore = this.getQuestionAbilityScore(
      hisQuestions,
      EAbility.PROBLEM_SOLVING_ABILITY
    )

    const solveAnswerScore = this.getAnswerAbilityScore(
      ability,
      hisQuestions,
      EAbility.PROBLEM_SOLVING_ABILITY
    )

    return {
      subjectId: query.subjectId,
      subjectName: subject.name,
      // !we need store count history because question count and correct answer may change
      questionCount: ability?.questionCount ?? subject.questions.length,
      correctCount:
        ability.correctCount ??
        this.getCorrectCount(ability?.answers || [], subject.questions),
      // totalScores: subject.questions
      //   .filter(this.filterQA)
      //   .map((item) =>
      //     item.ability.map((item) => item.score).reduce((acc, cur) => acc + cur, 0),
      //   )
      //   .reduce((acc, cur) => acc + cur, 0),
      totalTime: subject.countdown ? subject.countdown * subject.questions.length : 0,
      time: ability?.time || 0,
      // maxScores: ability?.scores || 0,
      ability: [
        {
          ability: EAbility.COMPREHENSION,
          scores:
            comprehensionScore > 0
              ? new Decimal(comprehensionAnswerScore)
                .div(comprehensionScore)
                .mul(100)
                .toNumber()
              : 0,
        },
        {
          ability: EAbility.EXPRESSION_ABILITY,
          scores:
            expressionScore > 0
              ? new Decimal(expressionAnswerScore)
                .div(expressionScore)
                .mul(100)
                .toNumber()
              : 0,
        },
        {
          ability: EAbility.INQUIRY_ABILITY,
          scores:
            inquiryScore > 0
              ? new Decimal(inquiryAnswerScore).div(inquiryScore).mul(100).toNumber()
              : 0,
        },
        {
          ability: EAbility.JUDGMENT_ABILITY,
          scores:
            judgmentScore > 0
              ? new Decimal(judgmentAnswerScore).div(judgmentScore).mul(100).toNumber()
              : 0,
        },
        {
          ability: EAbility.SKILLS_OF_ANALYZE,
          scores:
            analyzeScore > 0
              ? new Decimal(analyzeAnswerScore).div(analyzeScore).mul(100).toNumber()
              : 0,
        },
        {
          ability: EAbility.PROBLEM_SOLVING_ABILITY,
          scores:
            solveScore > 0
              ? new Decimal(solveAnswerScore).div(solveScore).mul(100).toNumber()
              : 0,
        },
      ].filter((v) => {
        if (hisAbilities.length && !hisAbilities.includes(v.ability)) {
          return false
        }
        return true
      }),
    }
  }

  private getBadge(
    answers: AnswerItem[],
    questions: Question[],
    countdown: number,
    time: number
  ) {
    let correctCount = 0
    const answerData = answers
      .map((item) => {
        const question = questions.find((question) => question.id === item.questionId)
        const isCorrect = question.options.every((option) =>
          option.isCorrect
            ? item.selectedOptionIds.includes(option.id)
            : !item.selectedOptionIds.includes(option.id)
        )
        return { questionId: item.questionId, isCorrect, sequence: question.sequence }
      })
      .sort((a, b) => a.sequence - b.sequence)

    answerData.forEach((item) => {
      if (item.isCorrect) {correctCount += 1}
      else {correctCount = 0}
    })

    let badge = []
    if (correctCount >= 8) {
      badge = [
        EBadge.ANSWER_THREE_QUESTION_CORRECTLY,
        EBadge.ANSWER_FIVE_QUESTION_CORRECTLY,
        EBadge.ANSWER_EIGHT_QUESTION_CORRECTLY,
      ]
    } else if (correctCount >= 5) {
      badge = [
        EBadge.ANSWER_THREE_QUESTION_CORRECTLY,
        EBadge.ANSWER_FIVE_QUESTION_CORRECTLY,
      ]
    } else if (correctCount >= 3) {
      badge = [EBadge.ANSWER_THREE_QUESTION_CORRECTLY]
    }

    if (
      correctCount === questions.length &&
      countdown &&
      time < (countdown * questions.length) / 2
    ) {
      badge.push(EBadge.ANSWER_QUESTION_CORRECTLY_AND_QUICKLY)
    }

    const comingBadge = this.getComingBadge(correctCount, questions.length, badge)
    return { badge, comingBadge }
  }

  // 即将获得徽章逻辑
  private getComingBadge(
    correctCount: number,
    questionCount: number,
    ownedBadge: EBadge[]
  ) {
    if (questionCount <= correctCount) {
      return
    }
    const map = {
      2: EBadge.ANSWER_THREE_QUESTION_CORRECTLY,
      4: EBadge.ANSWER_FIVE_QUESTION_CORRECTLY,
      7: EBadge.ANSWER_EIGHT_QUESTION_CORRECTLY,
    }
    const comingBadge = map[correctCount]
    console.log(correctCount, questionCount, ownedBadge)
    if (comingBadge != undefined && !ownedBadge.includes(comingBadge)) {
      return comingBadge
    }
  }

  private filterQA(v: Question) {
    return v.questionType === EQuestionType.MULTIPLE_CHOICE_QUESTIONS
  }

  private getAbilities(questions: Question[]) {
    return questions
      .filter(this.filterQA)
      .flatMap((item) => item.ability.filter((item) => item.score))
      .map((v) => v.ability)
  }

  private getQuestionAbilityScore(questions: Question[], ability: EAbility) {
    return questions
      .filter(this.filterQA)
      .filter((item) => item.ability.findIndex((item) => item.ability === ability) !== -1)
      .reduce(
        (acc, cur) =>
          acc + cur.ability.find((item) => item.ability === ability)?.score || 0,
        0
      )
  }

  private getAnswerAbilityScore(
    answers: UserAnswer,
    questions: Question[],
    ability: EAbility
  ) {
    return questions
      ?.filter(this.filterQA)
      ?.filter(
        (item) => item.ability?.findIndex((item) => item.ability === ability) !== -1
      )
      .map((item) => {
        const userAnswer = answers?.answers?.find(
          (answer) => answer.questionId === item.id
        )
        if (
          userAnswer &&
          item.options.every((option) =>
            option.isCorrect
              ? userAnswer.selectedOptionIds.includes(option.id)
              : !userAnswer.selectedOptionIds.includes(option.id)
          )
        ) {
          return item.ability.find((item) => item.ability === ability)?.score || 0
        }
        return 0
      })
      .reduce((acc, cur) => acc + cur, 0)
  }

  private getScores(answers: AnswerItem[], questions: Question[]) {
    console.log(
      questions.filter(this.filterQA).map((item) => {
        const userAnswer = answers?.find((answer) => answer.questionId === item.id)
        if (
          userAnswer &&
          item.options.every((option) =>
            option.isCorrect
              ? userAnswer.selectedOptionIds?.includes(option.id)
              : !userAnswer.selectedOptionIds?.includes(option.id)
          )
        ) {
          console.log(item)
          return (
            item.ability
              ?.map((item) => item.score)
              ?.reduce((acc, curr) => acc + curr, 0) || 0
          )
        }
        return 0
      })
    )
    return questions
      .filter(this.filterQA)
      .map((item) => {
        const userAnswer = answers?.find((answer) => answer.questionId === item.id)
        if (
          userAnswer &&
          item.options.every((option) =>
            option.isCorrect
              ? userAnswer.selectedOptionIds?.includes(option.id)
              : !userAnswer.selectedOptionIds?.includes(option.id)
          )
        ) {
          console.log(item)
          return (
            item.ability
              ?.map((item) => item.score)
              ?.reduce((acc, curr) => acc + curr, 0) || 0
          )
        }
        return 0
      })
      .reduce((acc, cur) => acc + cur, 0)
  }

  private getCorrectCount(answers: AnswerItem[], questions: Question[]) {
    return questions.filter(this.filterQA).filter((item) => {
      const userAnswer = answers?.find((answer) => answer.questionId === item.id)
      return (
        userAnswer &&
        item.options.every((option) =>
          option.isCorrect
            ? userAnswer.selectedOptionIds?.includes(option.id)
            : !userAnswer.selectedOptionIds?.includes(option.id)
        )
      )
    }).length
  }

  private getAnswerCacheKey(userId: number, subjectId: number) {
    return `user_answer:${userId}:${subjectId}`
  }

  private getAnswerCountCacheKey(userId: number, subjectId: number) {
    return `user_answer_count:${userId}:${subjectId}`
  }
}
