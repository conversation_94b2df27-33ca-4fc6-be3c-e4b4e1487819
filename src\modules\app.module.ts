import { CacheModule } from '@nestjs/cache-manager'
import { Module } from '@nestjs/common'
import redisStore from 'cache-manager-redis-store'
import { CommonModule } from '@/common' 
import { TaskModule } from '@/common/components/task' 
import config from '../config'
import { AccountModule } from './account'
import { AppController } from './app/controller'
import { DataExportTask } from './app/tasks'
import { AssistantModule } from './assistant/assistant.module'
import {
  AssistantFileTask,
  AssistantTask,
  CheckAssistantFilesCron,
} from './assistant/tasks'
import { BookModule } from './books/book.module'
import { SchoolsModule } from './schools/schools.module'
import { UpdateSchoolLevelTask } from './schools/tasks'
import { SharedModule } from './shared/shared.module'
import { PublicContriller } from './system/controllers/public.controller'
import { SystemModule } from './system/system.module'
import {
  ExpireReadingCache,
  HandleSchoolBalanceQueueService,
  SaveToDBService,
} from './tasks'
import { WebsocketModule } from './websocket'
@Module({
  imports: [
    CommonModule.forRoot(config, {
      withRedis: true,
      withTypeOrm: true,
      withMongo: true, 
    }),
    CacheModule.registerAsync({
      isGlobal: true,
      useFactory: () => ({
        store: redisStore,
        host: process.env.REDIS_HOST || '127.0.0.1',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD || '123456',
        ttl: 300,
        max: 10000,
      }),
    }),
    TaskModule,
    WebsocketModule,
    SystemModule,

    AccountModule,
    BookModule,
    SchoolsModule,
    AssistantModule,

    //
    SharedModule,
  ],
  providers: [
    DataExportTask,
    SaveToDBService,
    // ReadRecordRepository moved to SchoolsModule
    ExpireReadingCache,
    UpdateSchoolLevelTask,
    HandleSchoolBalanceQueueService,
    AssistantFileTask,
    // AssistantTask,
    CheckAssistantFilesCron,
  ],
  controllers: [
    PublicContriller,
    AppController,
  ],
})
export class AppModule {}
