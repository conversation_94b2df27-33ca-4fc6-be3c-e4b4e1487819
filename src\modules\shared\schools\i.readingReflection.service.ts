interface IReadingReflection {
    getReadingReflectionBookCount(userId: number, version: any): Promise<any>
  // 可根据需要继续添加其他方法声明
}

export abstract class IReadingReflectionService implements IReadingReflection {
    abstract getReadingReflectionBookCount(userId: number, version: any): Promise<any>
  // 可根据需要继续添加其他方法声明
    abstract getReadingReflectionCount(userId: number, bookId: number, version: any): Promise<any>
}