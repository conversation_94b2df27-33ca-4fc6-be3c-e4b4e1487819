import {ListAdministratorsRequest} from '@/modules/account'

interface IAdmin {
  // findOne(options: any): Promise<any>
  // find(options?: any): Promise<any[]>
}

export abstract class IAdminRepo implements IAdmin {
    // abstract findOne(options: any): Promise<any>
    // abstract find(options?: any): Promise<any[]>

    abstract count()

    abstract countByDay(query: { startTime: number; endTime: number })

    abstract listAdministrators(options?: ListAdministratorsRequest)
}