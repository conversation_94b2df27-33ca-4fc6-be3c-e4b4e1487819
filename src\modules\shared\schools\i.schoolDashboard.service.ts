import {ELocaleType} from '@/common'

interface ISchoolDashboard {}
export abstract class ISchoolDashboardService implements ISchoolDashboard {
    // abstract getSchoolDashboard(schoolId: number, query: any): Promise<any>
    // abstract exportSchoolData(schoolId: number, query: any): Promise<any>

    abstract exportReadingOfStudent(options: {
        local: ELocaleType
        schoolId: number
        user: any
        query: any
    })
}