interface IHomepage {
    getHomepage(id: number): Promise<any>
    createHomepage(data: any): Promise<any>
    updateHomepage(id: number, data: any): Promise<any>
    getTop10Books(schoolId: number, options?: any): Promise<any>
}

export abstract class IHomepageService implements IHomepage {
    abstract getHomepage(id: number): Promise<any>
    abstract createHomepage(data: any): Promise<any>
    abstract updateHomepage(id: number, data: any): Promise<any>
    abstract getTop10Books(schoolId: number, options?: any): Promise<any>
}