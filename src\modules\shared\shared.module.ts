import { Global, Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import {Administrator, Application, AppSetting, AppV<PERSON><PERSON>, Assistant, AssistantContracts, AssistantFiles, AssistantSessionCount, AssistantThread,
  AssistantThreadMessageRuns, Author, Book, BookLevel, BookList, BookNote, BookOperateApplication, BookReadingPos, BookReadingTime,
  Bookshelf, BookStatistics, Category, Chapter, ContactUs, Contract, ContractBook, ContractHistories, CountryCode, Grade, Homepage,
  HotSearchWord, Label, Log, Message, Notification, OperationLog, Permission, Publisher, Question, ReadRecord, RecommendSearchWord,
  ReferenceBook, ReferenceReadRecord, Region, Resource, Role, School, SchoolAdministrator, SchoolBalance, SchoolHomepage, SchoolPermission,
  SchoolRole, ScienceContracts, Subject, SubjectCategory, Theme, User, UserAnswer, UserAnswerCount, UserBalance, UserClass, ViewBookDetail,
} from '@/entities'
import { ReadingReflection } from '@/entities/readingReflection.entity'
import { Recharge } from '@/entities/recharge.entity'
import { ReferenceReadingReflection } from '@/entities/referenceReadingReflection.entity'
import {
  IAdminRepo, IFileTemplateService,
  ISchoolAdminRepo,
  IUserRepo,
  IUserService,
} from './account'
import {IAssistantContractsService} from './assistant'
import {
  IAuthorService,
  IBookLevelService,
  IBookRepo,
  IBookService,
  IBookshelfService,
  IInitLeaderBoardService,
  ILeaderBoardService,
  IPublisherService,
  IReadRecordService,
  IReadingPosService,
  IReferenceReadService,
} from './books'
import {
  IContractService,
  IGradeService,
  IHomepageService,
  IReadingReflectionService,
  IReadingTimeManagerService,
  ISchoolService,
  ISchoolDashboardService,
  IUserBalanceService,
  IUserClassService
} from './schools'

@Global()
@Module({
  exports: [

    // schools module
    IContractService,
    IGradeService,
    IHomepageService,
    IReadingReflectionService,
    IReadingTimeManagerService,
    ISchoolDashboardService,
    ISchoolService,
    IUserBalanceService,
    IUserClassService,

    // book services
    IAuthorService,
    IBookLevelService,
    IBookRepo,
    IBookService,
    IBookshelfService,
    IInitLeaderBoardService,
    ILeaderBoardService,
    IPublisherService,
    IReadRecordService,
    IReadingPosService,
    IReferenceReadService,

    // account module
    IAdminRepo,
    IFileTemplateService,
    ISchoolAdminRepo,
    IUserRepo,
    IUserService,

    // assistant module
    IAssistantContractsService,

    // TypeORM Module for all entities
    TypeOrmModule,
  ],

  providers: [
    // books module
    { provide: IAuthorService, useValue: null },
    { provide: IBookLevelService, useValue: null },
    { provide: IBookRepo, useValue: null },
    { provide: IBookService, useValue: null },
    { provide: IBookshelfService, useValue: null },
    { provide: IInitLeaderBoardService, useValue: null },
    { provide: ILeaderBoardService, useValue: null },
    { provide: IPublisherService, useValue: null },
    { provide: IReadRecordService, useValue: null },
    { provide: IReadingPosService, useValue: null },
    { provide: IReferenceReadService, useValue: null },

    // schools module
    { provide: IContractService, useValue: null },
    { provide: IGradeService, useValue: null },
    { provide: IHomepageService, useValue: null },
    { provide: IReadingReflectionService, useValue: null },
    { provide: IReadingTimeManagerService, useValue: null },
    { provide: ISchoolDashboardService, useValue: null },
    { provide: ISchoolService, useValue: null },
    { provide: IUserBalanceService, useValue: null },
    { provide: IUserClassService, useValue: null },

    // account module
    { provide: IAdminRepo, useValue: null },
    { provide: IFileTemplateService, useValue: null },
    { provide: ISchoolAdminRepo, useValue: null },
    { provide: IUserRepo, useValue: null },
    { provide: IUserService, useValue: null },

    // assistant module
    { provide: IAssistantContractsService, useValue: null },
  ],


  imports: [
    TypeOrmModule.forFeature([
      // User & Account entities
      User, Administrator, UserClass, UserBalance, SchoolAdministrator,

      // School entities
      School, SchoolBalance, SchoolHomepage, SchoolPermission, SchoolRole,

      // Book entities
      Book, BookLevel, BookList, BookNote, BookOperateApplication, BookReadingPos, BookReadingTime, Bookshelf, BookStatistics,

      // Content entities
      Author, Category, Chapter, Homepage, HotSearchWord, Label, Publisher, RecommendSearchWord,

      // Reading entities
      ReadRecord, ReferenceBook, ReferenceReadRecord, ReadingReflection, ReferenceReadingReflection, ViewBookDetail,

      // System entities
      Application,AppSetting,AppVersion,ContactUs,CountryCode,Grade,Log,Message,Notification,OperationLog,Permission,Region,Resource,Role,

      // Contract entities
      Contract, ContractBook, ContractHistories, ScienceContracts,

      // Question entities
      Question, Subject, SubjectCategory, Theme, UserAnswer, UserAnswerCount,

      // Assistant entities
      Assistant, AssistantContracts, AssistantFiles, AssistantSessionCount, AssistantThread, AssistantThreadMessageRuns,

      // Other entities
      Recharge,
    ]),
  ],
})
export class SharedModule {}
