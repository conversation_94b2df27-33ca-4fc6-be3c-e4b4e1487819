import {QueryPlatformStatisticViewBookDto} from '@/modules/books/dto/viewBook.dto'
import {ELocaleType} from '@/common'
import {QueryReadingTimeDto} from '@/modules/books/dto'


interface IViewBookDetail{

}
export abstract class IViewBookDetailService implements IViewBookDetail{
    abstract statisticsForPlatform(query: QueryPlatformStatisticViewBookDto)

    abstract export(query: QueryPlatformStatisticViewBookDto, locale: ELocaleType)

    abstract exportByAuthorBooks(query: QueryReadingTimeDto, locale: ELocaleType)

    abstract viewBookDetail(bookId: number, userId: number, schoolId: number)
}