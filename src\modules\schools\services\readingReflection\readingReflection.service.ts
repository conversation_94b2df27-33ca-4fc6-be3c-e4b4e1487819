import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Between, Repository } from 'typeorm'
import { Book, ReadRecord, ReferenceReadRecord, User } from '@/entities'
import { ReadingReflection } from '@/entities/readingReflection.entity'
import { ReferenceReadingReflection } from '@/entities/referenceReadingReflection.entity'
import { EBookReadRefectionType, EBookVersion, EUserType } from '@/enums'
import { IReadingReflectionService} from '@/modules/shared/interfaces'
import { SubmitReadingReflectionDto } from '../../dto/readingReflection.dto'
import {
  ReadingReflectionCountLimitException,
} from '../../exception'

@Injectable()
export class ReadingReflectionService implements IReadingReflectionService {
  constructor(
    @InjectRepository(ReadingReflection)
    private readonly readingReflectionRepository: Repository<ReadingReflection>,

    @InjectRepository(Book)
    private readonly bookRepository: Repository<Book>,

    @InjectRepository(User)
    private readonly userRepository: Repository<User>,

    @InjectRepository(ReferenceReadingReflection)
    private readonly referenceReadingReflectionRepository: Repository<ReferenceReadingReflection>,

    @InjectRepository(ReadRecord)
    private readonly readRecordRepository: Repository<ReadRecord>,

    @InjectRepository(ReferenceReadRecord)
    private readonly referenceReadRecordRepository: Repository<ReferenceReadRecord>
  ) {}

  /**
   * 提交阅读感想
   * @param user
   * @param data
   * @param type
   * @returns
   */
  async submitReadingReflection(
    user: any,
    data: SubmitReadingReflectionDto,
    type: EBookReadRefectionType
  ) {
    const { version = EBookVersion.SUBSCRIPTION } = data

    const repository =
      version === EBookVersion.SUBSCRIPTION
        ? this.readingReflectionRepository
        : this.referenceReadingReflectionRepository

    const count = await repository.count({
      where: {
        user: { id: user.userId },
        book: { id: data.bookId },
      },
    })

    if (count >= 15) {
      throw new ReadingReflectionCountLimitException()
    }

    await repository.save({
      userType: user.isTeacher ? EUserType.TEACHER : EUserType.STUDENT,
      gradeId: user.gradeId,
      user: { id: user.userId },
      userClass: { id: user.classId },
      school: { id: user.schoolId },
      book: { id: data.bookId },
      content: type === EBookReadRefectionType.TEXT ? data.content : null,
      audioUrl: type === EBookReadRefectionType.AUDIO ? data.audioUrl : null,
      audioTime: type === EBookReadRefectionType.AUDIO ? data.audioTime : null,
    })
    return {}
  }

  /**
   * 获取阅读感想次数
   * @param userId
   * @param bookId
   * @param version
   * @returns
   */
  async getReadingReflectionCount(userId: number, bookId: number, version: EBookVersion) {
    const repository =
      version === EBookVersion.SUBSCRIPTION
        ? this.readingReflectionRepository
        : this.referenceReadingReflectionRepository
    const alias =
      version === EBookVersion.SUBSCRIPTION
        ? 'reading_reflection'
        : 'reference_reading_reflection'
    const count = await repository
      .createQueryBuilder(alias)
      .select('COUNT(*)', 'count')
      .where(`${alias}.user_id = :userId`, { userId })
      .andWhere(`${alias}.book_id = :bookId`, { bookId })
      .getRawOne()

    return count ? parseInt(count?.count || 0, 10) : 0
  }

  /**
   * 获取阅读感想记录 书籍数
   * @param userId
   * @param version
   * @returns
   */
  async getReadingReflectionBookCount(userId: number, version: EBookVersion) {
    const repository =
      version === EBookVersion.SUBSCRIPTION
        ? this.readingReflectionRepository
        : this.referenceReadingReflectionRepository
    const alias =
      version === EBookVersion.SUBSCRIPTION
        ? 'reading_reflection'
        : 'reference_reading_reflection'
    const count = await repository
      .createQueryBuilder(alias)
      .select(`COUNT(DISTINCT ${alias}.book_id)`, 'count')
      .where(`${alias}.user_id = :userId`, { userId })
      .getRawOne()

    return count ? parseInt(count?.count || 0, 10) : 0
  }

  /**
   * 获取阅读感想记录内容 参考馆 ｜ 订阅版
   * @param userId
   * @param query
   * @returns
   */
  async getReadingReflectionInfo(userId: number, query: any) {
    const { version = EBookVersion.SUBSCRIPTION } = query
    const repository =
      version === EBookVersion.SUBSCRIPTION
        ? this.readingReflectionRepository
        : this.referenceReadingReflectionRepository

    const whereConditions: any = {
      user: { id: userId },
      book: { id: query.bookId },
    }

    if (query.startTime && query.endTime) {
      whereConditions.createdAt = Between(
        new Date(query.startTime * 1000),
        new Date(query.endTime * 1000)
      )
    }

    const count = await repository.count({
      where: {
        user: { id: userId },
        book: { id: query.bookId },
      },
    })

    const results = await repository.find({
      select: [
        'id',
        'content',
        'audioUrl',
        'audioTime',
        'flagState',
        'reviewState',
        'createdAt',
      ],
      where: whereConditions,
      relations: ['user', 'book'],
      order: { createdAt: 'DESC' },
    })

    return {
      userName: results[0]?.user?.givenName,
      bookName: results[0]?.book?.name,
      count,
      items: results.map((item) => ({
        id: item.id,
        content: item.content || '',
        flagState: item.flagState,
        reviewState: item.reviewState,
        audioUrl: item.audioUrl || '',
        audioTime: item.audioTime || 0,
        createdAt: item.createdAt,
      })),
    }
  }
}
