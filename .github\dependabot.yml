#version: 2
#updates:
#  - package-ecosystem: "npm"
#    directory: "/"
#    schedule:
#      interval: "weekly"  # 降低频率，减少冲突
#    open-pull-requests-limit: 5
#    versioning-strategy: "increase"  # 更宽松的版本策略
#    commit-message:
#      prefix: "chore(deps)"
#      include: "scope"
#    labels: ["dependencies"]
#    # 忽略有问题的依赖，让它们手动处理
#    ignore:
#      - dependency-name: "jest"
#        versions: ["< 29.0.0"]  # 忽略 jest 小版本更新
#      - dependency-name: "ws"
#        update-types: ["version-update:semver-major"]  # 忽略 ws 的大版本更新
#    # 只允许安全更新自动处理
#    allow:
#      - dependency-type: "direct"
#      - dependency-type: "indirect"