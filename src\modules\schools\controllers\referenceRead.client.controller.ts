import { Body, Controller, Post } from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'
import { ApiBaseResult, BooleanResponse, ClientAuth, CurrentUser } from '@/common'
import { ILeaderBoardService, IReferenceReadService } from '@/modules/shared/interfaces'
import { BookIDDto } from '../../books/dto'

@Controller('v1/client/reference-book/read-record')
export class ReferenceReadClientController {
  constructor(
    private readonly referenceReadService: IReferenceReadService,
    private readonly leaderBoardService: ILeaderBoardService,
  ) {}

  @ApiOperation({ summary: 'start read a reference book' })
  @ApiBaseResult(BooleanResponse, 200)
  @ClientAuth()
  @Post('start')
  async viewBookDetail(@CurrentUser() user: any, @Body() body: BookIDDto) {
    await this.referenceReadService.startRead(body.bookId, user.userId, user.schoolId)
    await this.leaderBoardService.increaseReferenceCount(user.schoolId, body.bookId)
    return { status: true }
  }

  @ApiOperation({ summary: 'reading a reference book' })
  @ApiBaseResult(BooleanResponse, 200)
  @ClientAuth()
  @Post('reading')
  async reading(@CurrentUser() user: any, @Body() body: BookIDDto) {
    await this.referenceReadService.reading(body.bookId, user.userId, user.schoolId)
    return { status: true }
  }

  @ApiOperation({ summary: 'end read a reference book' })
  @ApiBaseResult(BooleanResponse, 200)
  @ClientAuth()
  @Post('end')
  async endRead(@CurrentUser() user: any, @Body() body: BookIDDto) {
    await this.referenceReadService.endRead(body.bookId, user.userId, user.schoolId)
    return { status: true }
  }
}
