import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import R from 'ramda'
import { DataSource, In, Repository } from 'typeorm'
import { generateUniqueId } from '@/common'
import { BookList } from '@/entities'
import { EListStatus } from '@/enums'
import { PAGE_SIZE } from '@/modules/constants'
import {
  BookListNotExistException,
  BookNotExistException,
  DuplicateBookListUrlException,
} from '@/modules/exception'
import { nameLikeCondition } from '@/utils'
import { ModifyBookListDto, modifyBookListField, QueryBookListDto } from '../../books/dto'
import { IBookRepo } from '@/modules/shared/interfaces'

@Injectable()
export class BookListService {
  constructor(
    @InjectRepository(BookList) private readonly bookListRepository: Repository<BookList>,
    private readonly bookRepositories: IBookRepo,
    private readonly dataSource: DataSource
  ) {}

  async sortBooklistBooks(booklist: BookList) {
    const map = await this.getBooklistBookSorts(booklist.id)
    return {
      ...booklist,
      books: booklist.books.sort((a, b) => map[a.id] - map[b.id] || 0),
    }
  }

  async getBooklistBookSorts(id: number): Promise<{ [x: number]: number }> {
    const sorts = await this.bookListRepository
      .query(`select t1.books_id as book_id, @rownum:=@rownum+1 as sort from books_book_lists_book_lists t1, (SELECT (@rowNum :=0) ) t2 where t1.book_lists_id = ${id} order by created_at desc;
    `)

    return (sorts || []).reduce((response, item) => {
      response[Number(item.book_id)] = Number(item.sort)
      return response
    }, {})
  }

  async createBookList(data: ModifyBookListDto): Promise<BookList> {
    const books = await this.bookRepositories.searchBooks({
      ids: [...new Set(data.bookIds)],
    })
    if (books.length <= 0) {
      throw new BookNotExistException()
    }
    const booklistId = generateUniqueId()
    if (data.url) {
      const duplicateList = await this.bookListRepository.findOne({ where: { url: data.url } })
      if (duplicateList) {
        throw new DuplicateBookListUrlException()
      }
    }
    return this.bookListRepository.save({
      ...R.pick(modifyBookListField, data),
      books,
      url: data.url ?? booklistId,
      booklistId,
    })
  }

  async getBookList(options: { id?: number }): Promise<BookList> {
    const bookList = await this.bookListRepository.findOne({
      where: R.pick(['id'], options),
    })
    if (!bookList) {
      throw new BookListNotExistException()
    }
    return bookList
  }

  async findBookLists(options: { ids?: number[] }, relations?: string[]) {
    return this.bookListRepository.find({
      where: { id: In(options.ids) },
      relations,
    })
  }

  async findBookList(options: { id?: number; url?: string }): Promise<BookList> {
    const bookList = await this.bookListRepository.findOne({
      where: R.pick(['id', 'url'], options),
      relations: ['books', 'books.labels', 'books.authors', 'books.bookLevels'],
    })
    if (!bookList) {
      throw new BookListNotExistException()
    }
    return bookList
  }

  async updateBookList(id: number, data: ModifyBookListDto) {
    const bookListInit = []
    const bookList = await this.getBookList({ id })
    const books = await this.bookRepositories.searchBooks({
      ids: [...new Set(data.bookIds)],
    })
    if (books.length <= 0) {
      throw new BookNotExistException()
    }
    if (data.url) {
      const duplicateList = await this.bookListRepository.findOne({ where: { url: data.url } })
      if (duplicateList && duplicateList.id !== bookList.id) {
        throw new DuplicateBookListUrlException()
      }
    }
    await this.bookListRepository
      .createQueryBuilder()
      .relation('books')
      .of(bookList)
      .remove(
        await this.bookListRepository
          .createQueryBuilder()
          .relation('books')
          .of(bookList)
          .loadMany()
      )

    const list = { ...bookList, books, ...R.omit(['bookIds'], data) }

    if (data.status && data.status !== bookList.status) {
      if (data.status === EListStatus.ONLINE) {
        list.offlineAt = null
        list.onlineAt = new Date()
      } else if (data.status === EListStatus.OFFLINE) {
        list.offlineAt = new Date()
      }
    }

    return this.bookListRepository.save(list)
  }

  async updateBookListStatus(ids: number[], status: EListStatus) {
    return Promise.all(
      ids.map(async (id) => {
        const list = await this.getBookList({ id })
        if (list.status !== status) {
          const data: any = { status }
          if (status === EListStatus.ONLINE) {
            data.offlineAt = null
            data.onlineAt = new Date()
          } else if (status === EListStatus.OFFLINE) {
            data.offlineAt = new Date()
          }
          await this.bookListRepository.update({ id: In(ids) }, data)
          return list
        }
        return undefined
      })
    )
  }

  async listBookList(query: QueryBookListDto) {
    console.log(query)
    const { pageIndex = 1, pageSize = PAGE_SIZE, keyword } = query
    let where
    let parameter
    if (keyword) {
      const condition = nameLikeCondition(keyword, 'list')
      where = condition.where
      parameter = condition.parameter
    }
    if (!R.isNil(query.isBindHomepage)) {
      const bindCondition = query.isBindHomepage
        ? 'list.homepage_id is not null'
        : 'list.homepage_id is null'
      where = where ? `(${where}) and ${bindCondition}` : bindCondition
    }
    const builder = this.bookListRepository.createQueryBuilder('list')
    if (where) {
      builder.where(where, parameter)
    }

    const total = await builder.getCount()
    const items = await builder
      .leftJoinAndSelect('list.homepage', 'homepage')
      .leftJoinAndSelect('list.books', 'books')
      .take(pageSize)
      .skip((pageIndex - 1) * pageSize)
      .orderBy({ 'list.createdAt': 'DESC' })
      .getMany()

    return { total, items, pageIndex, pageSize }
  }

  async listAllBookListIds() {
    const list = await this.bookListRepository.find({ select: ['id'] })
    return list.map((item) => item.id)
  }

  async deleteBookList(ids: number[]) {
    await this.dataSource.transaction(async (manager) => {
      await manager.query(
        `delete from books_book_lists_book_lists where book_lists_id IN (${ids.join(
          ','
        )})`
      )
      await manager.delete(BookList, { id: In(ids) })
    })
  }
}
