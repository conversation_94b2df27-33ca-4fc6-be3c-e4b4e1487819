import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { In, Like, MoreThan, Repository } from 'typeorm'
import { ELocaleType, PageRequest } from '@/common'
import { Grade } from '@/entities'
import { PAGE_SIZE } from '@/modules/constants'
import { GRADES_LOCALE } from '../constants'
import { CreateGradeDto, SortGradeDto, UpdateGradeDto } from '../dto'
import { DuplicateGradeException } from '../exception'
import{IGradeService} from '@/modules/shared/interfaces'

@Injectable()
export class GradeService implements IGradeService {
  constructor(
    @InjectRepository(Grade)
    private readonly gradeRepository: Repository<Grade>
  ) {}

  async createGrade(schoolId: number, body: CreateGradeDto) {
    const reqGrade = body.grade
      ? body.grade
      : GRADES_LOCALE[ELocaleType.ZH_HK][body.gradeCode]
    const reqGradeCode = body.gradeCode
    const data = await this.gradeRepository.findOne({
      where: [
        {
          schoolId,
          grade: reqGrade,
          gradeCode: reqGradeCode,
        },
      ],
    })

    if (data && data.grade === reqGrade && data.gradeCode === reqGradeCode) {
      throw new DuplicateGradeException()
    }

    const maxGrade = await this.gradeRepository.findOne({
      where: { schoolId },
      order: { sequence: 'DESC' },
    })
    return this.gradeRepository.save({
      grade: reqGrade,
      gradeCode: reqGradeCode,
      schoolId,
      sequence: Number(maxGrade?.sequence || 0) + 1,
    })
  }

  async updateGradeSequence(data: SortGradeDto) {
    await Promise.all(
      data.grades.map((item) =>
        this.gradeRepository.update({ id: item.id }, { sequence: item.sequence })
      )
    )
  }

  async updateGrade(schoolId: number, id: number, body: UpdateGradeDto) {
    const reqGrade = body.grade
      ? body.grade
      : GRADES_LOCALE[ELocaleType.ZH_HK][body.gradeCode]
    const isExistGrade = await this.gradeRepository.findOne({ where: { schoolId, grade: reqGrade } })
    if (isExistGrade && isExistGrade.grade === reqGrade && isExistGrade.id !== id) {
      throw new DuplicateGradeException()
    }
    await this.gradeRepository.update(
      { id: id },
      { grade: body.grade, gradeCode: body.gradeCode }
    )
  }

  async listGrade(schoolId: number, query: PageRequest) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const items = await this.gradeRepository.find({
      where: { schoolId },
      take: pageSize,
      skip: (pageIndex - 1) * pageSize,
      order: { sequence: 'ASC' },
    })

    const total = await this.gradeRepository.countBy({ schoolId })

    return {
      total,
      items,
      pageIndex,
      pageSize,
    }
  }

  async listAllGrade(schoolId: number, keyword?: string) {
    const condition: any = { schoolId }

    if (keyword) {
      condition.grade = Like(`%${keyword}%`)
    }
    return this.gradeRepository.find({ where: condition, order: { sequence: 'ASC' } })
  }

  async listGrades(ids: number[]) {
    return this.gradeRepository.find({
      where: { id: In(ids) },
      order: { sequence: 'ASC' },
    })
  }

  async listGradesWithDelete(ids: number[]) {
    return this.gradeRepository.find({
      withDeleted: true,
      where: { id: In(ids) },
      order: { sequence: 'ASC' },
    })
  }

  async getGrade(id: number) {
    const grade = await this.gradeRepository.findOne({ where: { id } })
    if (!grade) {
      throw new NotFoundException('grade not found')
    }
    return grade
  }

  async findGradeWithoutCheck(grade: string, schoolId: number) {
    return this.gradeRepository.findOne({ where: { grade, schoolId } })
    // if (!data) {
    //   throw new NotFoundException('grade not found')
    // }
    // return data
  }

  async deleteGrade(id: number, deletedBy: any) {
    const grade = await this.gradeRepository.findOne({ where: { id } })
    if (!grade) {
      throw new NotFoundException('grade not found')
    }
    const sortGrades = await this.gradeRepository.find({
      where: { schoolId: grade.schoolId, sequence: MoreThan(grade.sequence) },
    })
    await this.gradeRepository.update(
      { id },
      { deletedAt: new Date(), deletedBy, grade: `${grade.grade} ${Date.now()}` }
    )
    await Promise.all(
      sortGrades.map((item) =>
        this.gradeRepository.update({ id: item.id }, { sequence: item.sequence - 1 })
      )
    )
  }
}
